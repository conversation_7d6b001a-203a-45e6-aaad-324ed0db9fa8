require_relative '../node_modules/react-native/scripts/react_native_pods'
require_relative '../node_modules/@react-native-community/cli-platform-ios/native_modules'


platform :ios, '11.0'
install! 'cocoapods', :deterministic_uuids => false
# add the Firebase pod for Google Analytics
# pod 'Google-Mobile-Ads-SDK', '~> 7.69'
pod 'Google-Mobile-Ads-SDK'
pod 'GoogleMobileAdsMediationFacebook'
# or pod 'Firebase/AnalyticsWithoutAdIdSupport'
# for Analytics without IDFA collection capability

# add pods for any other desired Firebase products
# https://firebase.google.com/docs/ios/setup#available-pods
target 'ChatClient' do
  config = use_native_modules!
# Flags change depending on the env values.
flags = get_default_flags()

  use_react_native!(
    :path => config[:reactNativePath],
    # to enable hermes on iOS, change `false` to `true` and then install pods
    :hermes_enabled => flags[:hermes_enabled],
    :fabric_enabled => flags[:fabric_enabled],
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )

  target 'ChatClientTests' do
    inherit! :complete
    # Pods for testing
  end

  # Enables Flipper.
  #
  # Note that if you have use_frameworks! enabled, Flipper will not work and
  # you should disable the next line.
  use_flipper!()

  post_install do |installer|
    react_native_post_install(installer)
    __apply_Xcode_12_5_M1_post_install_workaround(installer)
  end
end
