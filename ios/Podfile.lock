PODS:
  - boost (1.76.0)
  - CocoaAsyncSocket (7.6.5)
  - DoubleConversion (1.1.6)
  - FBAudienceNetwork (6.11.0)
  - FBLazyVector (0.68.2)
  - FBReactNativeSpec (0.68.2):
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTRequired (= 0.68.2)
    - RCTTypeSafety (= 0.68.2)
    - React-Core (= 0.68.2)
    - React-jsi (= 0.68.2)
    - ReactCommon/turbomodule/core (= 0.68.2)
  - Flipper (0.125.0):
    - Flipper-Folly (~> 2.6)
    - Flipper-RSocket (~> 1.4)
  - Flipper-Boost-iOSX (********.11)
  - Flipper-DoubleConversion (3.2.0)
  - Flipper-Fmt (7.1.7)
  - Flipper-Folly (2.6.10):
    - Flipper-Boost-iOSX
    - Flipper-DoubleConversion
    - Flipper-Fmt (= 7.1.7)
    - Flipper-Glog
    - libevent (~> 2.1.12)
    - OpenSSL-Universal (= 1.1.1100)
  - Flipper-Glog (*******)
  - Flipper-PeerTalk (0.0.4)
  - Flipper-RSocket (1.4.3):
    - Flipper-Folly (~> 2.6)
  - FlipperKit (0.125.0):
    - FlipperKit/Core (= 0.125.0)
  - FlipperKit/Core (0.125.0):
    - Flipper (~> 0.125.0)
    - FlipperKit/CppBridge
    - FlipperKit/FBCxxFollyDynamicConvert
    - FlipperKit/FBDefines
    - FlipperKit/FKPortForwarding
    - SocketRocket (~> 0.6.0)
  - FlipperKit/CppBridge (0.125.0):
    - Flipper (~> 0.125.0)
  - FlipperKit/FBCxxFollyDynamicConvert (0.125.0):
    - Flipper-Folly (~> 2.6)
  - FlipperKit/FBDefines (0.125.0)
  - FlipperKit/FKPortForwarding (0.125.0):
    - CocoaAsyncSocket (~> 7.6)
    - Flipper-PeerTalk (~> 0.0.4)
  - FlipperKit/FlipperKitHighlightOverlay (0.125.0)
  - FlipperKit/FlipperKitLayoutHelpers (0.125.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutTextSearchable
  - FlipperKit/FlipperKitLayoutIOSDescriptors (0.125.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutHelpers
    - YogaKit (~> 1.18)
  - FlipperKit/FlipperKitLayoutPlugin (0.125.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitHighlightOverlay
    - FlipperKit/FlipperKitLayoutHelpers
    - FlipperKit/FlipperKitLayoutIOSDescriptors
    - FlipperKit/FlipperKitLayoutTextSearchable
    - YogaKit (~> 1.18)
  - FlipperKit/FlipperKitLayoutTextSearchable (0.125.0)
  - FlipperKit/FlipperKitNetworkPlugin (0.125.0):
    - FlipperKit/Core
  - FlipperKit/FlipperKitReactPlugin (0.125.0):
    - FlipperKit/Core
  - FlipperKit/FlipperKitUserDefaultsPlugin (0.125.0):
    - FlipperKit/Core
  - FlipperKit/SKIOSNetworkPlugin (0.125.0):
    - FlipperKit/Core
    - FlipperKit/FlipperKitNetworkPlugin
  - fmt (6.2.1)
  - glog (0.3.5)
  - Google-Mobile-Ads-SDK (9.6.0):
    - GoogleAppMeasurement (< 10.0, >= 7.0)
    - GoogleUserMessagingPlatform (>= 1.1)
  - GoogleAppMeasurement (9.1.0):
    - GoogleAppMeasurement/AdIdSupport (= 9.1.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.7)
    - GoogleUtilities/MethodSwizzler (~> 7.7)
    - GoogleUtilities/Network (~> 7.7)
    - "GoogleUtilities/NSData+zlib (~> 7.7)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (9.1.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 9.1.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.7)
    - GoogleUtilities/MethodSwizzler (~> 7.7)
    - GoogleUtilities/Network (~> 7.7)
    - "GoogleUtilities/NSData+zlib (~> 7.7)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (9.1.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.7)
    - GoogleUtilities/MethodSwizzler (~> 7.7)
    - GoogleUtilities/Network (~> 7.7)
    - "GoogleUtilities/NSData+zlib (~> 7.7)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleMobileAdsMediationFacebook (6.11.0.0):
    - FBAudienceNetwork (= 6.11.0)
    - Google-Mobile-Ads-SDK (~> 9.0)
  - GoogleUserMessagingPlatform (2.0.0)
  - GoogleUtilities/AppDelegateSwizzler (7.7.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
  - GoogleUtilities/Environment (7.7.0):
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.7.0):
    - GoogleUtilities/Environment
  - GoogleUtilities/MethodSwizzler (7.7.0):
    - GoogleUtilities/Logger
  - GoogleUtilities/Network (7.7.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.7.0)"
  - GoogleUtilities/Reachability (7.7.0):
    - GoogleUtilities/Logger
  - libevent (2.1.12)
  - nanopb (2.30909.0):
    - nanopb/decode (= 2.30909.0)
    - nanopb/encode (= 2.30909.0)
  - nanopb/decode (2.30909.0)
  - nanopb/encode (2.30909.0)
  - OpenSSL-Universal (1.1.1100)
  - PromisesObjC (2.1.0)
  - RCT-Folly (2021.06.28.00-v2):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
    - RCT-Folly/Default (= 2021.06.28.00-v2)
  - RCT-Folly/Default (2021.06.28.00-v2):
    - boost
    - DoubleConversion
    - fmt (~> 6.2.1)
    - glog
  - RCTRequired (0.68.2)
  - RCTTypeSafety (0.68.2):
    - FBLazyVector (= 0.68.2)
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTRequired (= 0.68.2)
    - React-Core (= 0.68.2)
  - React (0.68.2):
    - React-Core (= 0.68.2)
    - React-Core/DevSupport (= 0.68.2)
    - React-Core/RCTWebSocket (= 0.68.2)
    - React-RCTActionSheet (= 0.68.2)
    - React-RCTAnimation (= 0.68.2)
    - React-RCTBlob (= 0.68.2)
    - React-RCTImage (= 0.68.2)
    - React-RCTLinking (= 0.68.2)
    - React-RCTNetwork (= 0.68.2)
    - React-RCTSettings (= 0.68.2)
    - React-RCTText (= 0.68.2)
    - React-RCTVibration (= 0.68.2)
  - React-callinvoker (0.68.2)
  - React-Codegen (0.68.2):
    - FBReactNativeSpec (= 0.68.2)
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTRequired (= 0.68.2)
    - RCTTypeSafety (= 0.68.2)
    - React-Core (= 0.68.2)
    - React-jsi (= 0.68.2)
    - React-jsiexecutor (= 0.68.2)
    - ReactCommon/turbomodule/core (= 0.68.2)
  - React-Core (0.68.2):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default (= 0.68.2)
    - React-cxxreact (= 0.68.2)
    - React-jsi (= 0.68.2)
    - React-jsiexecutor (= 0.68.2)
    - React-perflogger (= 0.68.2)
    - Yoga
  - React-Core/CoreModulesHeaders (0.68.2):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.68.2)
    - React-jsi (= 0.68.2)
    - React-jsiexecutor (= 0.68.2)
    - React-perflogger (= 0.68.2)
    - Yoga
  - React-Core/Default (0.68.2):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-cxxreact (= 0.68.2)
    - React-jsi (= 0.68.2)
    - React-jsiexecutor (= 0.68.2)
    - React-perflogger (= 0.68.2)
    - Yoga
  - React-Core/DevSupport (0.68.2):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default (= 0.68.2)
    - React-Core/RCTWebSocket (= 0.68.2)
    - React-cxxreact (= 0.68.2)
    - React-jsi (= 0.68.2)
    - React-jsiexecutor (= 0.68.2)
    - React-jsinspector (= 0.68.2)
    - React-perflogger (= 0.68.2)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.68.2):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.68.2)
    - React-jsi (= 0.68.2)
    - React-jsiexecutor (= 0.68.2)
    - React-perflogger (= 0.68.2)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.68.2):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.68.2)
    - React-jsi (= 0.68.2)
    - React-jsiexecutor (= 0.68.2)
    - React-perflogger (= 0.68.2)
    - Yoga
  - React-Core/RCTBlobHeaders (0.68.2):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.68.2)
    - React-jsi (= 0.68.2)
    - React-jsiexecutor (= 0.68.2)
    - React-perflogger (= 0.68.2)
    - Yoga
  - React-Core/RCTImageHeaders (0.68.2):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.68.2)
    - React-jsi (= 0.68.2)
    - React-jsiexecutor (= 0.68.2)
    - React-perflogger (= 0.68.2)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.68.2):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.68.2)
    - React-jsi (= 0.68.2)
    - React-jsiexecutor (= 0.68.2)
    - React-perflogger (= 0.68.2)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.68.2):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.68.2)
    - React-jsi (= 0.68.2)
    - React-jsiexecutor (= 0.68.2)
    - React-perflogger (= 0.68.2)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.68.2):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.68.2)
    - React-jsi (= 0.68.2)
    - React-jsiexecutor (= 0.68.2)
    - React-perflogger (= 0.68.2)
    - Yoga
  - React-Core/RCTTextHeaders (0.68.2):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.68.2)
    - React-jsi (= 0.68.2)
    - React-jsiexecutor (= 0.68.2)
    - React-perflogger (= 0.68.2)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.68.2):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default
    - React-cxxreact (= 0.68.2)
    - React-jsi (= 0.68.2)
    - React-jsiexecutor (= 0.68.2)
    - React-perflogger (= 0.68.2)
    - Yoga
  - React-Core/RCTWebSocket (0.68.2):
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Core/Default (= 0.68.2)
    - React-cxxreact (= 0.68.2)
    - React-jsi (= 0.68.2)
    - React-jsiexecutor (= 0.68.2)
    - React-perflogger (= 0.68.2)
    - Yoga
  - React-CoreModules (0.68.2):
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTTypeSafety (= 0.68.2)
    - React-Codegen (= 0.68.2)
    - React-Core/CoreModulesHeaders (= 0.68.2)
    - React-jsi (= 0.68.2)
    - React-RCTImage (= 0.68.2)
    - ReactCommon/turbomodule/core (= 0.68.2)
  - React-cxxreact (0.68.2):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-callinvoker (= 0.68.2)
    - React-jsi (= 0.68.2)
    - React-jsinspector (= 0.68.2)
    - React-logger (= 0.68.2)
    - React-perflogger (= 0.68.2)
    - React-runtimeexecutor (= 0.68.2)
  - React-jsi (0.68.2):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-jsi/Default (= 0.68.2)
  - React-jsi/Default (0.68.2):
    - boost (= 1.76.0)
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
  - React-jsiexecutor (0.68.2):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-cxxreact (= 0.68.2)
    - React-jsi (= 0.68.2)
    - React-perflogger (= 0.68.2)
  - React-jsinspector (0.68.2)
  - React-logger (0.68.2):
    - glog
  - react-native-admob-native-ads (0.6.0):
    - React-Core
  - react-native-camera (4.2.1):
    - React-Core
    - react-native-camera/RCT (= 4.2.1)
    - react-native-camera/RN (= 4.2.1)
  - react-native-camera/RCT (4.2.1):
    - React-Core
  - react-native-camera/RN (4.2.1):
    - React-Core
  - react-native-cameraroll (4.1.2):
    - React-Core
  - react-native-image-resizer (1.4.5):
    - React-Core
  - react-native-safe-area-context (3.4.1):
    - React-Core
  - react-native-splash-screen (3.3.0):
    - React-Core
  - react-native-tracking-transparency (0.1.1):
    - React
  - React-perflogger (0.68.2)
  - React-RCTActionSheet (0.68.2):
    - React-Core/RCTActionSheetHeaders (= 0.68.2)
  - React-RCTAnimation (0.68.2):
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTTypeSafety (= 0.68.2)
    - React-Codegen (= 0.68.2)
    - React-Core/RCTAnimationHeaders (= 0.68.2)
    - React-jsi (= 0.68.2)
    - ReactCommon/turbomodule/core (= 0.68.2)
  - React-RCTBlob (0.68.2):
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Codegen (= 0.68.2)
    - React-Core/RCTBlobHeaders (= 0.68.2)
    - React-Core/RCTWebSocket (= 0.68.2)
    - React-jsi (= 0.68.2)
    - React-RCTNetwork (= 0.68.2)
    - ReactCommon/turbomodule/core (= 0.68.2)
  - React-RCTImage (0.68.2):
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTTypeSafety (= 0.68.2)
    - React-Codegen (= 0.68.2)
    - React-Core/RCTImageHeaders (= 0.68.2)
    - React-jsi (= 0.68.2)
    - React-RCTNetwork (= 0.68.2)
    - ReactCommon/turbomodule/core (= 0.68.2)
  - React-RCTLinking (0.68.2):
    - React-Codegen (= 0.68.2)
    - React-Core/RCTLinkingHeaders (= 0.68.2)
    - React-jsi (= 0.68.2)
    - ReactCommon/turbomodule/core (= 0.68.2)
  - React-RCTNetwork (0.68.2):
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTTypeSafety (= 0.68.2)
    - React-Codegen (= 0.68.2)
    - React-Core/RCTNetworkHeaders (= 0.68.2)
    - React-jsi (= 0.68.2)
    - ReactCommon/turbomodule/core (= 0.68.2)
  - React-RCTSettings (0.68.2):
    - RCT-Folly (= 2021.06.28.00-v2)
    - RCTTypeSafety (= 0.68.2)
    - React-Codegen (= 0.68.2)
    - React-Core/RCTSettingsHeaders (= 0.68.2)
    - React-jsi (= 0.68.2)
    - ReactCommon/turbomodule/core (= 0.68.2)
  - React-RCTText (0.68.2):
    - React-Core/RCTTextHeaders (= 0.68.2)
  - React-RCTVibration (0.68.2):
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-Codegen (= 0.68.2)
    - React-Core/RCTVibrationHeaders (= 0.68.2)
    - React-jsi (= 0.68.2)
    - ReactCommon/turbomodule/core (= 0.68.2)
  - React-runtimeexecutor (0.68.2):
    - React-jsi (= 0.68.2)
  - ReactCommon/turbomodule/core (0.68.2):
    - DoubleConversion
    - glog
    - RCT-Folly (= 2021.06.28.00-v2)
    - React-callinvoker (= 0.68.2)
    - React-Core (= 0.68.2)
    - React-cxxreact (= 0.68.2)
    - React-jsi (= 0.68.2)
    - React-logger (= 0.68.2)
    - React-perflogger (= 0.68.2)
  - RNCAsyncStorage (1.17.5):
    - React-Core
  - RNCClipboard (1.5.1):
    - React-Core
  - RNCPushNotificationIOS (1.10.1):
    - React-Core
  - RNDeviceInfo (8.7.1):
    - React-Core
  - RNFS (2.20.0):
    - React-Core
  - RNGestureHandler (2.4.2):
    - React-Core
  - RNIap (8.0.9):
    - React-Core
  - RNImageMarker (0.6.3):
    - React
  - RNRate (1.2.9):
    - React-Core
  - RNScreens (3.13.1):
    - React-Core
    - React-RCTImage
  - RNSVG (12.3.0):
    - React-Core
  - RNVectorIcons (9.1.0):
    - React-Core
  - SocketRocket (0.6.0)
  - Yoga (1.14.0)
  - YogaKit (1.18.1):
    - Yoga (~> 1.14)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native/React/FBReactNativeSpec`)
  - Flipper (= 0.125.0)
  - Flipper-Boost-iOSX (= ********.11)
  - Flipper-DoubleConversion (= 3.2.0)
  - Flipper-Fmt (= 7.1.7)
  - Flipper-Folly (= 2.6.10)
  - Flipper-Glog (= *******)
  - Flipper-PeerTalk (= 0.0.4)
  - Flipper-RSocket (= 1.4.3)
  - FlipperKit (= 0.125.0)
  - FlipperKit/Core (= 0.125.0)
  - FlipperKit/CppBridge (= 0.125.0)
  - FlipperKit/FBCxxFollyDynamicConvert (= 0.125.0)
  - FlipperKit/FBDefines (= 0.125.0)
  - FlipperKit/FKPortForwarding (= 0.125.0)
  - FlipperKit/FlipperKitHighlightOverlay (= 0.125.0)
  - FlipperKit/FlipperKitLayoutPlugin (= 0.125.0)
  - FlipperKit/FlipperKitLayoutTextSearchable (= 0.125.0)
  - FlipperKit/FlipperKitNetworkPlugin (= 0.125.0)
  - FlipperKit/FlipperKitReactPlugin (= 0.125.0)
  - FlipperKit/FlipperKitUserDefaultsPlugin (= 0.125.0)
  - FlipperKit/SKIOSNetworkPlugin (= 0.125.0)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - Google-Mobile-Ads-SDK
  - GoogleMobileAdsMediationFacebook
  - OpenSSL-Universal (= 1.1.1100)
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/DevSupport (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - react-native-admob-native-ads (from `../node_modules/react-native-admob-native-ads`)
  - react-native-camera (from `../node_modules/react-native-camera`)
  - "react-native-cameraroll (from `../node_modules/@react-native-community/cameraroll`)"
  - react-native-image-resizer (from `../node_modules/react-native-image-resizer`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - react-native-splash-screen (from `../node_modules/react-native-splash-screen`)
  - react-native-tracking-transparency (from `../node_modules/react-native-tracking-transparency`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNCClipboard (from `../node_modules/@react-native-community/clipboard`)"
  - "RNCPushNotificationIOS (from `../node_modules/@react-native-community/push-notification-ios`)"
  - RNDeviceInfo (from `../node_modules/react-native-device-info`)
  - RNFS (from `../node_modules/react-native-fs`)
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNIap (from `../node_modules/react-native-iap`)
  - RNImageMarker (from `../node_modules/react-native-image-marker`)
  - RNRate (from `../node_modules/react-native-rate`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - RNSVG (from `../node_modules/react-native-svg`)
  - RNVectorIcons (from `../node_modules/react-native-vector-icons`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - CocoaAsyncSocket
    - FBAudienceNetwork
    - Flipper
    - Flipper-Boost-iOSX
    - Flipper-DoubleConversion
    - Flipper-Fmt
    - Flipper-Folly
    - Flipper-Glog
    - Flipper-PeerTalk
    - Flipper-RSocket
    - FlipperKit
    - fmt
    - Google-Mobile-Ads-SDK
    - GoogleAppMeasurement
    - GoogleMobileAdsMediationFacebook
    - GoogleUserMessagingPlatform
    - GoogleUtilities
    - libevent
    - nanopb
    - OpenSSL-Universal
    - PromisesObjC
    - SocketRocket
    - YogaKit

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native/React/FBReactNativeSpec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  react-native-admob-native-ads:
    :path: "../node_modules/react-native-admob-native-ads"
  react-native-camera:
    :path: "../node_modules/react-native-camera"
  react-native-cameraroll:
    :path: "../node_modules/@react-native-community/cameraroll"
  react-native-image-resizer:
    :path: "../node_modules/react-native-image-resizer"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-splash-screen:
    :path: "../node_modules/react-native-splash-screen"
  react-native-tracking-transparency:
    :path: "../node_modules/react-native-tracking-transparency"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNCClipboard:
    :path: "../node_modules/@react-native-community/clipboard"
  RNCPushNotificationIOS:
    :path: "../node_modules/@react-native-community/push-notification-ios"
  RNDeviceInfo:
    :path: "../node_modules/react-native-device-info"
  RNFS:
    :path: "../node_modules/react-native-fs"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNIap:
    :path: "../node_modules/react-native-iap"
  RNImageMarker:
    :path: "../node_modules/react-native-image-marker"
  RNRate:
    :path: "../node_modules/react-native-rate"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  RNVectorIcons:
    :path: "../node_modules/react-native-vector-icons"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  boost: a7c83b31436843459a1961bfd74b96033dc77234
  CocoaAsyncSocket: 065fd1e645c7abab64f7a6a2007a48038fdc6a99
  DoubleConversion: 831926d9b8bf8166fd87886c4abab286c2422662
  FBAudienceNetwork: 2eee2f90ecb812ba761949366845d5e5efe7a0a1
  FBLazyVector: a7a655862f6b09625d11c772296b01cd5164b648
  FBReactNativeSpec: 81ce99032d5b586fddd6a38d450f8595f7e04be4
  Flipper: 26fc4b7382499f1281eb8cb921e5c3ad6de91fe0
  Flipper-Boost-iOSX: fd1e2b8cbef7e662a122412d7ac5f5bea715403c
  Flipper-DoubleConversion: 3d3d04a078d4f3a1b6c6916587f159dc11f232c4
  Flipper-Fmt: 60cbdd92fc254826e61d669a5d87ef7015396a9b
  Flipper-Folly: 584845625005ff068a6ebf41f857f468decd26b3
  Flipper-Glog: 87bc98ff48de90cb5b0b5114ed3da79d85ee2dd4
  Flipper-PeerTalk: 116d8f857dc6ef55c7a5a75ea3ceaafe878aadc9
  Flipper-RSocket: d9d9ade67cbecf6ac10730304bf5607266dd2541
  FlipperKit: cbdee19bdd4e7f05472a66ce290f1b729ba3cb86
  fmt: ff9d55029c625d3757ed641535fd4a75fedc7ce9
  glog: 476ee3e89abb49e07f822b48323c51c57124b572
  Google-Mobile-Ads-SDK: bdf13d37aa77e368510687b5305d4972b4f9e9c7
  GoogleAppMeasurement: 8ecc717f2abe3f9ee95a5d38db08827852894acc
  GoogleMobileAdsMediationFacebook: d83aecc66aa19e68e4539d47568415ea8aabd80d
  GoogleUserMessagingPlatform: ab890ce5f6620f293a21b6bdd82e416a2c73aeca
  GoogleUtilities: e0913149f6b0625b553d70dae12b49fc62914fd1
  libevent: 4049cae6c81cdb3654a443be001fb9bdceff7913
  nanopb: b552cce312b6c8484180ef47159bc0f65a1f0431
  OpenSSL-Universal: ebc357f1e6bc71fa463ccb2fe676756aff50e88c
  PromisesObjC: 99b6f43f9e1044bd87a95a60beff28c2c44ddb72
  RCT-Folly: 4d8508a426467c48885f1151029bc15fa5d7b3b8
  RCTRequired: 3e917ea5377751094f38145fdece525aa90545a0
  RCTTypeSafety: c43c072a4bd60feb49a9570b0517892b4305c45e
  React: 176dd882de001854ced260fad41bb68a31aa4bd0
  React-callinvoker: c2864d1818d6e64928d2faf774a3800dfc38fe1f
  React-Codegen: 98b6f97f0a7abf7d67e4ce435c77c05b7a95cf05
  React-Core: fdaa2916b1c893f39f02cff0476d1fb0cab1e352
  React-CoreModules: fd8705b80699ec36c2cdd635c2ce9d874b9cfdfc
  React-cxxreact: 1832d971f7b0cb2c7b943dc0ec962762c90c906e
  React-jsi: 72af715135abe8c3f0dcf3b2548b71d048b69a7e
  React-jsiexecutor: b7b553412f2ec768fe6c8f27cd6bafdb9d8719e6
  React-jsinspector: c5989c77cb89ae6a69561095a61cce56a44ae8e8
  React-logger: a0833912d93b36b791b7a521672d8ee89107aff1
  react-native-admob-native-ads: 90a26c7f7662950af516fd378768f0d35c0368f2
  react-native-camera: 3eae183c1d111103963f3dd913b65d01aef8110f
  react-native-cameraroll: 2957f2bce63ae896a848fbe0d5352c1bd4d20866
  react-native-image-resizer: d9fb629a867335bdc13230ac2a58702bb8c8828f
  react-native-safe-area-context: 9e40fb181dac02619414ba1294d6c2a807056ab9
  react-native-splash-screen: 4312f786b13a81b5169ef346d76d33bc0c6dc457
  react-native-tracking-transparency: b2029ff756f1128b1f2c7c7c7f3003bc3c950f9f
  React-perflogger: a18b4f0bd933b8b24ecf9f3c54f9bf65180f3fe6
  React-RCTActionSheet: 547fe42fdb4b6089598d79f8e1d855d7c23e2162
  React-RCTAnimation: bc9440a1c37b06ae9ebbb532d244f607805c6034
  React-RCTBlob: a1295c8e183756d7ef30ba6e8f8144dfe8a19215
  React-RCTImage: a30d1ee09b1334067fbb6f30789aae2d7ac150c9
  React-RCTLinking: ffc6d5b88d1cb9aca13c54c2ec6507fbf07f2ac4
  React-RCTNetwork: f807a2facab6cf5cf36d592e634611de9cf12d81
  React-RCTSettings: 861806819226ed8332e6a8f90df2951a34bb3e7f
  React-RCTText: f3fb464cc41a50fc7a1aba4deeb76a9ad8282cb9
  React-RCTVibration: 79040b92bfa9c3c2d2cb4f57e981164ec7ab9374
  React-runtimeexecutor: b960b687d2dfef0d3761fbb187e01812ebab8b23
  ReactCommon: 095366164a276d91ea704ce53cb03825c487a3f2
  RNCAsyncStorage: 81187912cc9a5c2591ddb161e03e587688afb764
  RNCClipboard: 41d8d918092ae8e676f18adada19104fa3e68495
  RNCPushNotificationIOS: 87b8d16d3ede4532745e05b03c42cff33a36cc45
  RNDeviceInfo: aad3c663b25752a52bf8fce93f2354001dd185aa
  RNFS: 4ac0f0ea233904cb798630b3c077808c06931688
  RNGestureHandler: 61628a2c859172551aa2100d3e73d1e57878392f
  RNIap: db71948776eb280b9d11cef00fdfb1126bbf30a0
  RNImageMarker: 76cd113306bcfad2f74f78d83d5accfd50b4434a
  RNRate: 94f57c773e155ca0d0aeeba9c10a32bce9030daf
  RNScreens: 40a2cb40a02a609938137a1e0acfbf8fc9eebf19
  RNSVG: 302bfc9905bd8122f08966dc2ce2d07b7b52b9f8
  RNVectorIcons: 7923e585eaeb139b9f4531d25a125a1500162a0b
  SocketRocket: fccef3f9c5cedea1353a9ef6ada904fde10d6608
  Yoga: 99652481fcd320aefa4a7ef90095b95acd181952
  YogaKit: f782866e155069a2cca2517aafea43200b01fd5a

PODFILE CHECKSUM: 71c4cee2ec06cf93c6a17ca34cd8694a41088c36

COCOAPODS: 1.11.3
