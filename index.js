/**
 * @format
 */

import { AppRegistry } from 'react-native';
import App from './App';

import Config from "react-native-config";
import PushNotification, { Importance } from 'react-native-push-notification';
import { MobileAds } from 'react-native-google-mobile-ads';
import remoteConfig from '@react-native-firebase/remote-config';
//flatlist freeze bug

// eslint-disable-next-line no-restricted-imports
import ViewReactNativeStyleAttributes from 'react-native/Libraries/Components/View/ReactNativeStyleAttributes';
ViewReactNativeStyleAttributes.scaleY = true;
console.log("env", JSON.stringify(Config));
globalThis.RNFB_SILENCE_MODULAR_DEPRECATION_WARNINGS = true;
const {
  appName,
  AdInterval,
  AppOpenAdId,
  AdInterstitial,
  AdBanner,
  BoredBannerOne,
  BoredBannerTwo,
  BoredNative,
  BoredNativeTwo,
  BoredNativeOne,
} = Config;
remoteConfig()
  .setDefaults({
    AdInterval,
    AppOpenAdId,
    AdInterstitial,
    AdBanner,
    BoredBannerOne,
    BoredBannerTwo,
    BoredNative,
    BoredNativeOne,
    BoredNativeTwo,
    addGamesTab: false,
  })
  .then(() => remoteConfig().fetchAndActivate());
MobileAds().initialize();

PushNotification.configure({
  permissions: {
    alert: true,
    badge: true,
    sound: true,
  },
  popInitialNotification: true,
});
PushNotification.createChannel(
  {
    channelId: 'default-channel-id', // (required)
    channelName: 'Default channel', // (required)
    // channelDescription: "A default channel", // (optional) default: undefined.
    soundName: 'default', // (optional) See `soundName` parameter of `localNotification` function
    importance: Importance.HIGH, // (optional) default: Importance.HIGH. Int value of the Android notification importance
    vibrate: true, // (optional) default: true. Creates the default vibration patten if true.
  },
  created =>
    console.log(`createChannel 'default-channel-id' returned '${created}'`), // (optional) callback returns whether the channel was created, false means it already existed.
);
console.log(`App Name: ${appName}`);

AppRegistry.registerComponent(appName, () => App);
