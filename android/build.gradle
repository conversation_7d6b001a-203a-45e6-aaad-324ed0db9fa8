buildscript {
    ext {
        buildToolsVersion = "35.0.0"
        minSdkVersion = 24
        compileSdkVersion = 35
        targetSdkVersion = 35
        ndkVersion = "26.1.10909125"
        kotlinVersion = "1.9.24"
        billingVersion = "6.1.0"
    }
    repositories {
        google()
        mavenCentral()
        // Add repositories for ad network SDKs
        maven { url "https://artifact.bytedance.com/repository/pangle" }
        maven { url "https://cboost.jfrog.io/artifactory/chartboost-ads/" }
    }
    dependencies {
        classpath("com.android.tools.build:gradle")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin")
        classpath 'com.google.gms:google-services:4.4.2'
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        // Add repositories for ad network SDKs
        maven { url "https://artifact.bytedance.com/repository/pangle" }
        maven { url "https://cboost.jfrog.io/artifactory/chartboost-ads/" }
        // Jitpack for other dependencies
        maven { url "https://jitpack.io" }
    }
}

apply plugin: "com.facebook.react.rootproject"
