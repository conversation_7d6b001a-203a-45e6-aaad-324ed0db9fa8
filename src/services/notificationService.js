import PushNotification from 'react-native-push-notification';
export function notifyUser(text, ignoreInForeground) {
  PushNotification.localNotification({
    id: 1,
    channelId: 'default-channel-id',
    ignoreInForeground: ignoreInForeground,
    vibrate: true,
    vibration: 300,
    playSound: true,
    title: 'Stranger:',
    soundName: 'default',
    message: text,
    userInfo: {id: 1},
    date: Date.now(),
    bigLargeIcon: 'ic_launcher',
    smallIcon: 'ic_notification',
  });
}
