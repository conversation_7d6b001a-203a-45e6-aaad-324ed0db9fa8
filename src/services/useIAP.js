import { useEffect, useState, useCallback } from 'react';
import { iapService } from './iapService';
import { ToastAndroid } from 'react-native';

export const useIAP = () => {
    const [adFree, setLocalAdFree] = useState(false);
    const [removeAdsProduct, setRemoveAdsProduct] = useState(null);
    const [adFreeOneMonthProduct, setAdFreeOneMonthProduct] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    
    const validateSubscription = useCallback(async () => {
        try {
            const isAdFree = await iapService.isAdFree();

            // Update local state
            setLocalAdFree(isAdFree);


            return isAdFree;
        } catch (error) {
            console.error('Error validating subscription:', error);
            return false;
        }
    }, []);

    useEffect(() => {
        const initialize = async () => {
            try {
                await iapService.init();
                
                // Validate current subscription status
                await validateSubscription();

                // Load products
                const [removeAds, adFreeOneMonth] = await Promise.all([
                    iapService.getRemoveAdsProduct(),
                    iapService.getAdFreeOneMonthProduct()
                ]);

                setRemoveAdsProduct(removeAds);
                setAdFreeOneMonthProduct(adFreeOneMonth);
            } catch (error) {
                console.error('Error initializing IAP:', error);
            }
        };

        initialize();

        return () => {
            iapService.cleanup();
        };
    }, [validateSubscription]);

    const subscribeRemoveAds = async () => {
        setIsLoading(true);
        try {
            const success = await iapService.subscribeRemoveAds(
                removeAdsProduct,
                async (productId, purchaseStatus) => {
                    if (purchaseStatus) {
                        ToastAndroid.show('Thank you for purchasing Remove Ads!', ToastAndroid.SHORT);
                        setLocalAdFree(true);
                    }
                    
                }
            );
            return success;
        } finally {
            setIsLoading(false);
        }
    };

    const purchaseAdFreeOneMonth = async () => {
        setIsLoading(true);
        try {
            const success = await iapService.purchaseAdFreeOneMonth(
                async () => {
                    ToastAndroid.show('Thank you for purchasing Ad-Free for one month!', ToastAndroid.SHORT);
                    setLocalAdFree(true);
                }
            );
            return success;
        } finally {
            setIsLoading(false);
        }
    };

    return { 
        adFree, 
        adFreeForOneMonth: adFree, // For backward compatibility with Settings.js
        removeAdsProduct, 
        adFreeOneMonthProduct, 
        purchaseRemoveAds: subscribeRemoveAds, 
        purchaseAdFreeOneMonth,
        isLoading,
        validateSubscription // Expose validation function for manual checks
    };
};
