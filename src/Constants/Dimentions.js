import {Dimensions, Platform} from 'react-native';
import {initialWindowMetrics} from 'react-native-safe-area-context';

export const CONTENT_SPACING = 15;

const insets = {
  bottom: initialWindowMetrics?.insets?.bottom || 0,
  left: initialWindowMetrics?.insets?.left || 0,
  top: initialWindowMetrics?.insets?.top || 0,
  right: initialWindowMetrics?.insets?.right || 0,
};
const SAFE_BOTTOM =
  Platform.select({
    ios: insets.bottom,
  }) ?? 0;

export const SAFE_AREA_PADDING = {
  paddingLeft: insets.left + CONTENT_SPACING,
  paddingTop: insets.top + CONTENT_SPACING,
  paddingRight: insets.right + CONTENT_SPACING,
  paddingBottom: SAFE_BOTTOM + CONTENT_SPACING,
};

// The maximum zoom _factor_ you should be able to zoom in
export const MAX_ZOOM_FACTOR = 20;

export const SCREEN_WIDTH = Dimensions.get('window').width;
export const SCREEN_HEIGHT = Platform.select({
  android: Dimensions.get('screen').height - insets.bottom,
  ios: Dimensions.get('window').height,
});

// Capture Button
export const CAPTURE_BUTTON_SIZE = 78;
