import React from 'react';
import {View, Text, TouchableOpacity, BackHandler} from 'react-native';
import BackgroundSection from '../Components/BackgroundSection';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {useSetRecoilState} from 'recoil';
import {TooltipState} from '../store/tooltip/atoms';
import {Stage} from '../store/tooltip/enum';

const UserAgreement = props => {
  const setToolTipVisible = useSetRecoilState(TooltipState);
  let ignoreBack = () => true;

  React.useEffect(() => {
    let backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      ignoreBack,
    );
    return () => {
      backHandler.remove();
    };
  }, []);
  let openTerms = () => {
    props.navigation.navigate('TermsConditions');
  };
  let AcceptTerms = () => {
    setToolTipVisible(Stage.Camera);
    AsyncStorage.setItem('termsAcceptance', 'true');
    props.navigation.navigate('Home');
  };
  return (
    <BackgroundSection>
      <View style={styles.container}>
        <View style={styles.outline}>
          <Text style={styles.title}>Welcome to Friendly Chat</Text>
          <Text style={styles.followRules}>
            Please Confirm that you can follow these rules and accept the
            Privacy Policy and User Terms before you start
          </Text>
          <View style={styles.disallowed}>
            <Text style={styles.label}>No Nudity/Sexual Content</Text>
            <Text style={styles.label}>No Spam & Scamming</Text>
            <Text style={styles.label}>No Violence & Physical Harm</Text>
            <Text style={styles.label}>No Hate speech & Racism</Text>
          </View>
          <TouchableOpacity onPress={openTerms}>
            <Text style={styles.userPolicy}>Privacy Policy & User Terms</Text>
          </TouchableOpacity>
          <TouchableOpacity onPress={AcceptTerms} style={styles.button}>
            <Text style={styles.buttonLabel}>Accept</Text>
          </TouchableOpacity>
        </View>
      </View>
    </BackgroundSection>
  );
};
const styles = {
  container: {
    flex: 1,
    justifyContent: 'center',
    flexDirection: 'column',
    alignItems: 'center',
    marginHorizontal: 40,
  },
  outline: {
    backgroundColor: '#E6A063',
    borderRadius: 20,
  },
  title: {
    fontSize: 18,
    margin: 10,
    alignSelf: 'center',
    color: 'black',
  },

  button: {
    backgroundColor: '#136AC7',
    borderRadius: 5,
    padding: 10,
    width: 100,
    alignSelf: 'center',
    marginVertical: 30,
  },

  buttonDisabled: {
    backgroundColor: '#999',
    borderRadius: 5,
    padding: 10,
  },

  buttonLabel: {
    fontSize: 14,
    color: '#FFF',
    alignSelf: 'center',
  },
  label: {
    marginHorizontal: 10,
    fontSize: 15,
    color: 'black',
    marginVertical: 20,
  },
  followRules: {
    fontSize: 10,
    marginHorizontal: 20,
    alignSelf: 'center',
  },
  disallowed: {
    borderRadius: 10,
    backgroundColor: 'white',
    marginVertical: 10,
    marginHorizontal: 30,
  },
  userPolicy: {
    fontSize: 12,
    marginHorizontal: 20,
    alignSelf: 'center',
    textDecorationLine: 'underline',
  },
};

export default UserAgreement;
