import React, { useContext, useEffect, useState } from 'react';
import { Platform, StyleSheet, View, ToastAndroid, ScrollView, Alert } from 'react-native';
import { useIAP } from '../services/useIAP';
import {
  Button,
  List,
  RadioButton,
  TextInput,
  Text,
  useTheme,
  Surface,
} from 'react-native-paper';
import SocketService from '../services/socketIoService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  BigHead,
  theme as bigHeadTheme,
  accessoryMap,
  clothingMap,
  eyebrowsMap,
  eyesMap,
  facialHairMap,
  hairMap,
  hatMap,
  mouthsMap,
} from 'react-native-bigheads';
import AvatarListItem from '../Components/Messages/AvatarListItem';
import BackgroundSection from '../Components/BackgroundSection';
import SettingsContext from '../contexts/SettingsContext';
import Tooltip from 'react-native-walkthrough-tooltip';
import { useRecoilState } from 'recoil';
import { TooltipState } from '../store/tooltip/atoms';
import { Stage } from '../store/tooltip/enum';
import { generateName } from '../services/nameGenerator';
import * as filter from 'leo-profanity';

function Settings({ navigation }) {
  const theme = useTheme();
  const {
    adFree,
    adFreeForOneMonth,
    removeAdsProduct,
    adFreeOneMonthProduct,
    purchaseRemoveAds,
    purchaseAdFreeOneMonth,
    isLoading
  } = useIAP();
  const context = useContext(SettingsContext);
  const [toolTipStage, setToolTipStage] = useRecoilState(TooltipState);
  const [name, onChangeName] = React.useState(context?.name || '');
  const [namePlaceHolder, onChangeNamePlaceHolder] = React.useState('');
  const [gender, onGenderChange] = React.useState(context?.gender || 'm');
  const [loading, setLoading] = React.useState(false);
  const [avatar, onAvatarChange] = React.useState(context?.avatar || {
    accessory: 'none',
    clothing: 'shirt',
    clothingColor: 'blue',
    eyebrows: 'raised',
    eyes: 'normal',
    facialHair: 'none',
    hair: 'short',
    hairColor: 'black',
    hat: 'none',
    mouth: 'grin',
    skinTone: 'light',
  });

  const randomAvatar = () =>
    onAvatarChange({
      accessory: selectRandomKey(accessoryMap),
      clothing: selectRandomKey(clothingMap),
      clothingColor: selectRandomKey(bigHeadTheme.colors.clothing),
      eyebrows: selectRandomKey(eyebrowsMap),
      eyes: selectRandomKey(eyesMap),
      facialHair: selectRandomKey(facialHairMap),
      hair: selectRandomKey(hairMap),
      hairColor: selectRandomKey(bigHeadTheme.colors.hair),
      hat: selectRandomKey(hatMap),
      mouth: selectRandomKey(mouthsMap),
      skinTone: selectRandomKey(bigHeadTheme.colors.skin),
    });

  const refreshName = () => {
    console.log('refreshName');
    const name = generateName();
    onChangeNamePlaceHolder(generateName());
    console.log('refreshName', name);
  }

  useEffect(() => {
    if (!avatar.accessory) {
      randomAvatar();
    }
    if (!name) {
      refreshName();
    }
  }, []);

  const selectRandomKey = object => {
    return Object.keys(object)[
      Math.floor(Math.random() * Object.keys(object).length)
    ];
  };

  const saveSettings = async () => {
    if (!context?.setSettings) {
      Alert.alert('Error', 'Unable to save settings. Please try again later.');
      return;
    }

    try {
      setLoading(true);
      const settings = {
        name: filter.clean(name || namePlaceHolder || generateName()),
        gender: gender || 'm',
        avatar: avatar || {
          accessory: 'none',
          clothing: 'shirt',
          clothingColor: 'blue',
          eyebrows: 'raised',
          eyes: 'normal',
          facialHair: 'none',
          hair: 'short',
          hairColor: 'black',
          hat: 'none',
          mouth: 'grin',
          skinTone: 'light',
        },
      };

      await Promise.all([
        AsyncStorage.setItem('settings', JSON.stringify(settings)),
        new Promise(resolve => {
          SocketService.emit('saveSettings', settings);
          resolve();
        })
      ]);

      context.setSettings(settings);

      if (Platform.OS === 'android') {
        ToastAndroid.showWithGravity(
          'Settings Saved!',
          ToastAndroid.SHORT,
          ToastAndroid.CENTER,
        );
      } else {
        Alert.alert('Success', 'Settings Saved!');
      }

      navigation.goBack();
    } catch (error) {
      console.error('Failed to save settings:', error);
      Alert.alert('Error', 'Failed to save settings. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const updateAvatar = (key, value) => {
    onAvatarChange({ ...avatar, [key]: value });
  };

  return (
    <BackgroundSection>
      <ScrollView contentContainerStyle={styles.container}>
        <Surface style={styles.surface} elevation={2}>
          <TextInput
            mode="outlined"
            placeholder={namePlaceHolder}
            value={name}
            onChangeText={onChangeName}
            onFocus={() => onChangeNamePlaceHolder('Name')}
            clearTextOnFocus={true}
            style={styles.input}
            right={
              <TextInput.Icon
                icon="refresh"
                onPress={refreshName}
                forceTextInputFocus={false}
              />
            }
          />

          <RadioButton.Group onValueChange={onGenderChange} value={gender}>
            <View style={styles.radioGroup}>
              <View style={styles.radioItem}>
                <RadioButton value="m" />
                <Text variant="bodyMedium">Male</Text>
              </View>
              <View style={styles.radioItem}>
                <RadioButton value="f" />
                <Text variant="bodyMedium">Female</Text>
              </View>
            </View>
          </RadioButton.Group>

          <View style={styles.avatarContainer}>
            <BigHead
              {...avatar}
              hatColor="white"
              lipColor="pink"
              lashes={false}
              graphic="react"
              bgShape="circle"
              bgColor={theme.colors.primary}
              body={gender === 'm' ? 'chest' : 'breasts'}
              size={150}
            />
          </View>

          <Tooltip
            isVisible={toolTipStage === Stage.Avatar}
            content={<Text variant="bodyMedium">Toggle to customize your avatar</Text>}
            placement="top"
            useInteractionManager={true}
            onClose={() => setToolTipStage(Stage.End)}
            showChildInTooltip={false}>
            {avatar && (
              <>
                <List.Accordion
                  style={styles.accordion}
                  title="Build your Avatar"
                  titleStyle={styles.titleStyle}
                  left={props => <List.Icon {...props} icon="account-edit" />}>
                  <AvatarListItem
                    value={avatar.accessory}
                    list={accessoryMap}
                    propName="accessory"
                    action={updateAvatar}
                  />
                  <AvatarListItem
                    value={avatar.clothing}
                    list={clothingMap}
                    propName="clothing"
                    action={updateAvatar}
                  />
                  <AvatarListItem
                    value={avatar.clothingColor}
                    list={bigHeadTheme.colors.clothing}
                    propName="clothingColor"
                    action={updateAvatar}
                  />
                  <AvatarListItem
                    value={avatar.eyebrows}
                    list={eyebrowsMap}
                    propName="eyebrows"
                    action={updateAvatar}
                  />
                  <AvatarListItem
                    value={avatar.eyes}
                    list={eyesMap}
                    propName="eyes"
                    action={updateAvatar}
                  />
                  <AvatarListItem
                    value={avatar.facialHair}
                    list={facialHairMap}
                    propName="facialHair"
                    action={updateAvatar}
                  />
                  <AvatarListItem
                    value={avatar.hair}
                    list={hairMap}
                    propName="hair"
                    action={updateAvatar}
                  />
                  <AvatarListItem
                    value={avatar.hairColor}
                    list={bigHeadTheme.colors.hair}
                    propName="hairColor"
                    action={updateAvatar}
                  />
                  <AvatarListItem
                    value={avatar.hat}
                    list={hatMap}
                    propName="hat"
                    action={updateAvatar}
                  />
                  <AvatarListItem
                    value={avatar.mouth}
                    list={mouthsMap}
                    propName="mouth"
                    action={updateAvatar}
                  />
                  <AvatarListItem
                    value={avatar.skinTone}
                    list={bigHeadTheme.colors.skin}
                    propName="skinTone"
                    action={updateAvatar}
                  />
                </List.Accordion>
                <Button
                  mode="contained-tonal"
                  icon="refresh"
                  onPress={randomAvatar}
                  style={styles.button}>
                  Randomize Avatar
                </Button>
              </>
            )}
          </Tooltip>


          {/* <View style={styles.iapButtonContainer}>
            <Button
              mode="contained"
              onPress={async () => {
                const success = await purchaseRemoveAds();
                if (success) {
                  Alert.alert('Success', 'Ad-Free activated! Enjoy ad-free experience.');
                }
              }}
              loading={isLoading}
              disabled={isLoading || adFree}
              style={[styles.button, styles.iapButton]}
              contentStyle={styles.buttonContent}
              labelStyle={styles.buttonLabel}
              icon="star"> 
              {adFree ? 'Ad-Free Active' : `Upgrade to Ad-Free ${removeAdsProduct?.localizedPrice ?? ''}`}
            </Button>

            <Button
              mode="contained-tonal"
              onPress={async () => {
                const success = await purchaseAdFreeOneMonth();
                if (success) {
                  Alert.alert('Success', 'Monthly Ad-Free activated! Enjoy ad-free experience for 30 days.');
                }
              }}
              loading={isLoading}
              disabled={isLoading || adFreeForOneMonth}
              style={[styles.button, styles.iapButton]}
              contentStyle={styles.buttonContent}
              labelStyle={styles.buttonLabel}
              icon="clock">
              {adFree ? 'Monthly Ad-Free Active' : `Try 1 Month Ad-Free ${adFreeOneMonthProduct?.localizedPrice ?? ''}`}
            </Button>
          </View> */}

          <View style={styles.buttonContainer}>
            <Button
              mode="contained"
              onPress={saveSettings}
              loading={loading}
              disabled={loading}
              style={styles.button}
              contentStyle={styles.buttonContent}
              labelStyle={styles.buttonLabel}>
              Save
            </Button>
            <Button
              mode="outlined"
              onPress={() => navigation.goBack()}
              disabled={loading}
              style={styles.button}
              contentStyle={styles.buttonContent}
              labelStyle={styles.buttonLabel}>
              Cancel
            </Button>
          </View>
        </Surface>
      </ScrollView>
    </BackgroundSection>
  );
}

const styles = StyleSheet.create({
  iapButtonContainer: {
    marginVertical: 16,
    gap: 8,
  },
  iapButton: {
    width: '100%',
    marginVertical: 4,
  },
  container: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 16,
  },
  surface: {
    padding: 20,
    borderRadius: 16,
    backgroundColor: 'white',
  },
  input: {
    marginBottom: 16,
  },
  radioGroup: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 16,
  },
  radioItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 16,
  },
  accordion: {
    marginVertical: 16,
    backgroundColor: 'transparent',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 16,
  },
  button: {
    minWidth: 120,
    marginHorizontal: 8,
  },
  buttonContent: {
    paddingVertical: 8,
  },
  buttonLabel: {
    fontSize: 16,
    fontFamily: Platform.select({
      ios: 'Poppins-Regular',
      android: 'PoppinsRegular-B2Bw',
    }),
  },
  avatarContainer: {
    alignItems: 'center',
    marginVertical: 16,
  },
  titleStyle: {
    marginBottom: 8,
    fontFamily: Platform.select({
      ios: 'Poppins-Regular',
      android: 'PoppinsRegular-B2Bw',
    }),
  },
});

export default Settings;
