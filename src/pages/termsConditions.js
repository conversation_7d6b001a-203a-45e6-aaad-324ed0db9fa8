import React, {Component} from 'react';
import {View, Text, ScrollView, TouchableOpacity} from 'react-native';
import BackgroundSection from '../Components/BackgroundSection';

const isCloseToBottom = ({layoutMeasurement, contentOffset, contentSize}) => {
  const paddingToBottom = 20;
  return (
    layoutMeasurement.height + contentOffset.y >=
    contentSize.height - paddingToBottom
  );
};

class TermsAndConditions extends Component {
  state = {
    accepted: false,
  };

  render() {
    return (
      <BackgroundSection>
        <View style={styles.container}>
          <ScrollView
            style={styles.tcContainer}
            onScroll={({nativeEvent}) => {
              if (isCloseToBottom(nativeEvent)) {
                this.setState({
                  accepted: true,
                });
              }
            }}>
            <Text style={styles.title}>Terms and conditions</Text>
            <Text style={styles.tcL}>
              The following terms & conditions constitute a binding legal
              agreement between You and Friendly Chat. (hereinafter referred to
              as Friendly Chat/ (which expression shall include its successors
              and assigns), setting forth the terms and conditions under which
              the Friendly Chat​ and any related services​ will be licensed to
              You by Friendly Chat.
            </Text>
            <Text style={styles.title}>1. Terms</Text>
            <Text style={styles.tcL}>
              By accessing, downloading, installing the Application, and using
              the Services, You represent to Friendly Chat that You are
              competent to enter into a contract (i.e. You are above 12 years of
              age, have a sound mind and are not disqualified from entering into
              a contract under the applicable law) and You have read this
              Agreement, understand it and agree to be bound by its Terms and
              are responsible for compliance with any applicable local laws.
              Please review the Agreement carefully before accessing,
              downloading and installation. If you do not agree with any of
              these terms, you are prohibited from using or accessing this app.
            </Text>
            <Text style={styles.title}>2. License</Text>
            <Text style={styles.tcL}>
              {'\u2022'}
              Subject to the Terms and compliance of the Terms hereof Friendly
              Chat shall grant You a license to use the Services provided that
              (a) You shall use the Application solely for Your personal and
              lawful use only; (b) You will not, nor allow third parties on Your
              behalf (i) to resell or charge others for use of the Application
              (ii) to duplicate, disassemble, decompile, transfer, exchange or
              translate the Application, create derivative works of the
              Application of any kind whatsoever or attempt to reverse engineer,
              alter or modify any part of the Application; and (c) You warrant
              to otherwise comply with the terms and conditions of this
              Agreement (the “License”).
            </Text>
            <Text style={styles.tcL}>
              {'\u2022'}
              This license shall automatically terminate if you violate any of
              these restrictions and may be terminated by Friendly Chat at any
              time. Upon terminating your viewing of these materials or upon the
              termination of this license, you must destroy any downloaded
              materials in your possession whether in electronic or printed
              format.
            </Text>
            <Text style={styles.tcL}>
              {'\u2022'} For avoidance of any doubt this License is personal,
              nonexclusive, non transferable, non-sub licensable, revocable and
              a limited license to download and use the Application on a
              electronic device that You own or control.
            </Text>
            <Text style={styles.title}>3. Privacy Policy</Text>
            <Text style={styles.tcL}>
              {'\u2022'}This Agreement is deemed to incorporate the Privacy
              Policy posted on the same document and accessible/embedded in the
              Application.
            </Text>
            <Text style={styles.title}>4. Disclaimer of warranties</Text>
            <Text style={styles.tcL}>
              {'\u2022'}YOU EXPRESSLY ACKNOWLEDGE AND AGREE THAT, TO THE MAXIMUM
              EXTENT PERMITTED BY APPLICABLE LAW, YOUR USE OF THE APPLICATION IS
              AT YOUR SOLE RISK AND DISCRETION.
            </Text>
            <Text style={styles.tcL}>
              {'\u2022'}
              THE APPLICATION IS LICENSED "AS-IS," "WITH ALL FAULTS," AND "AS
              AVAILABLE." YOU BEAR THE RISK OF USING IT. Friendly Chat, ON
              BEHALF OF ITSELF, GOOGLE, AMAZON, ALL WIRELESS CARRIERS OVER WHOSE
              NETWORK THE APPLICATION IS DISTRIBUTED, AND EACH OF OUR RESPECTIVE
              AFFILIATES, AND SUPPLIERS (“DISTRIBUTORS”), GIVES NO EXPRESS
              WARRANTIES, GUARANTEES, OR CONDITIONS UNDER OR IN RELATION TO THE
              APPLICATION. YOU MAY HAVE ADDITIONAL CONSUMER RIGHTS UNDER YOUR
              LOCAL LAWS WHICH THIS AGREEMENT CANNOT CHANGE. TO THE EXTENT
              PERMITTED UNDER YOUR LOCAL LAWS, DISTRIBUTORS EXCLUDE ANY IMPLIED
              WARRANTIES OR CONDITIONS, INCLUDING THOSE OF MERCHANTABILITY,
              FITNESS FOR A PARTICULAR PURPOSE AND NON- INFRINGEMENT.
            </Text>
            <Text style={styles.tcL}>
              {'\u2022'}LIMITATION ON AND EXCLUSION OF REMEDIES AND DAMAGES. TO
              THE EXTENT NOT PROHIBITED BY LAW, YOU CAN RECOVER FROM Friendly
              Chat ONLY DIRECT DAMAGES UP TO THE AMOUNT YOU PAID FOR THE
              APPLICATION. YOU AGREE NOT TO SEEK TO RECOVER ANY OTHER DAMAGES,
              INCLUDING CONSEQUENTIAL, LOST PROFITS, SPECIAL, INDIRECT OR
              INCIDENTAL DAMAGES FROM ANY DISTRIBUTOR. Friendly Chat MAKES NO
              WARRANTY AGAINST INTERFERENCE OF YOUR ENJOYMENT OF THE APPLICATION
              AND RELATED SERVICES; FOR LOSSES OR DAMAGES INCLUDING ANY PERSONAL
              INJURY ARISING FROM OR IN ANY WAY RELATED TO YOUR ACCESS OR USE OF
              THE APPLICATION, USE OF ANY CONTENT POSTED, TRANSMITTED, OR
              OTHERWISE MADE AVAILABLE VIA THE APPLICATION OR FOLLOWING A
              FAILURE, SUSPENSION OR WITHDRAWAL OF ALL OR PART OF THE
              APPLICATION AT ANY TIME; ANY BUGS, VIRUSES, OR THE LIKE WHICH MAY
              BE TRANSMITTED TO OR THROUGH OUR APPLICATION OR RELATED SERVICES,
              THAT THE APPLICATION WILL BE FUNCTIONAL, UNINTERRUPTED, ERROR-FREE
              OR BUG-FREE OR MEET YOUR REQUIREMENTS; REGARDING THE SECURITY,
              RELIABILITY OR TIMELINESS OF THE APPLICATION; THAT ANY ERRORS,
              BUGS OR FAILURES IN THE APPLICATION WILL BE CORRECTED.
              ACCORDINGLY, YOU EXPRESSLY AGREE THAT USE OF APPLICATION AND
              RELATED SERVICES IS PURELY VOLUNTARY ON YOUR PART, AT YOUR OWN
              RISK AND THEREFORE AGREE TO BEAR ANY AND ALL RISK WHATSOEVER
              AND/OR HOWSOEVER CAUSED. ANY CONTENT OR MATERIAL DOWNLOADED
              THROUGH YOUR USE OF THE APPLICATION IS AT YOUR OWN DISCRETION AND
              RISK AND YOU WILL BE SOLELY RESPONSIBLE FOR ANY DAMAGE OR LOSS OF
              DATA OCCURRING ON YOUR DEVICE OR ANY OTHER LOSS OR DAMAGES OF ANY
              KIND RESULTING FROM THE DOWNLOAD AND USE OF THE APPLICATION. NO
              ADVICE, COURSE OF CONDUCT OR INFORMATION, WHETHER ORAL OR WRITTEN,
              OBTAINED BY YOU FROMFriendlyChat OR ANY PARTY OR THROUGH THE
              APPLICATION SHALL CREATE ANY WARRANTY UNLESS EXPRESSLY STATED IN
              THIS AGREEMENT.
            </Text>
            <Text style={styles.tcL}>
              {'\u2022'}WHILST Friendly Chat SHALL UNDERTAKE ALL REASONABLE
              EFFORTS AND DUE DILIGENCE TO ENSURE SECURITY AND INTEGRITY,
              Friendly Chat SHALL, IN PARTICULAR, NOT BE LIABLE FOR THE
              FOLLOWING: DELAY OR ERRORS IN TRANSMISSION AND/OR STORAGE OF
              INFORMATION TO OR THROUGH Friendly Chat THAT MIGHT OCCUR FROM TIME
              TO TIME; INTRUSION, DISTORTION, LOSS OR FORGERY OF DATA, ETC DUE
              TO ACT OF ANY THIRD PARTY, FAILURE OF ANY SOFTWARE AND/OR HARDWARE
              OR TELECOMMUNICATION SERVICE PROVIDER(S) USED BY US OR ANY OTHER
              ACT BEYOND OUR REASONABLE CONTROL. YOU SHALL BE LIABLE FOR ANY
              CONSEQUENCES WHATSOEVER RESULTING FROM ANYTHING TRANSMITTED OR
              CAUSED TO BE TRANSMITTED BY YOU, OR TRANSMITTED TO YOU THROUGH THE
              APPLICATION.
            </Text>
            <Text style={styles.title}>5. Limitation of Liability</Text>
            <Text style={styles.tcP}>
              To the maximum extent permitted by applicable laws, under no
              circumstance shall Friendly Chat, its employees or agents be
              liable to You or any third person for personal injury, or any
              special, incidental, indirect, punitive or consequential damages
              whatsoever, including, but not limited to, damages for loss of
              profits or revenues, goodwill, failure to transmit or receive any
              data, loss of confidential information, business interruption,
              loss of privacy, corruption or loss of data, failure to receive or
              backup Your data (or archived data), for any cause of action,
              including contract, tort (including negligence) or otherwise and
              any other loss whatsoever arising out of or in any way arising
              from or related to the use of Application, or following a failure,
              suspension or withdrawal of all or part of the Application at any
              time, any third party content, software or functions used in
              connection with the Application even if Friendly Chat or any or
              all of its agents have been advised of the possibility of such
              damages.
            </Text>
            <Text style={styles.title}>6. Revisions and Errata</Text>

            <Text style={styles.tcP}>
              The materials appearing on Friendly Chat could include technical,
              typographical, or photographic errors. Friendly Chat does not
              warrant that any of the materials on its web site/app are
              accurate, complete, or current. Friendly Chat may make changes to
              the materials contained on its app/site at any time without
              notice.Friendly Chat does not, however, make any commitment to
              update the materials.
            </Text>
            <Text style={styles.title}>7. Amendments to this Agreement</Text>

            <Text style={styles.tcP}>
              We may in our sole discretion amend this Agreement from time to
              time without any prior notice. This may include adding new or
              different terms to, or removing terms from, this Agreement. Your
              use of the Application upon publication of such amended terms
              shall constitute your deemed acceptance to the amended Agreement.
            </Text>
            <Text style={styles.title}>8. Governing Law</Text>

            <Text style={styles.tcP}>
              This Agreement shall be governed by the laws of Egypt. You
              irrevocably consent to the exclusive jurisdiction of courts in
              Cairo,Egypt for all disputes arising out of or relating to this
              Agreement.
            </Text>
            <Text style={styles.title}>9. Contact Us</Text>

            <Text style={styles.tcP}>
              In the event you have any queries/complaints regarding usage of
              Application, please contact <NAME_EMAIL>.
            </Text>
            <Text style={styles.title}>Partnership</Text>
            <Text style={styles.tcP}>
              The friendly Chat app is having a signed agreement with atmegame
              company to show their sites in the app in exchange with revenues
              splitting
            </Text>

            <Text style={styles.title}>Privacy Policy</Text>

            <Text style={styles.tcP}>
              Friendly Chat. ​ provides this Privacy Policy to inform users of
              the Friendly Chat software application​ about our policies and
              procedures regarding the collection, use and disclosure of
              Personally Identifiable Information defined below.
            </Text>
            <Text style={styles.tcP}>
              The Application is intended for people who are competent to
              contract under applicable law. Individuals who are under 12 years
              old are not permitted to use this application. You confirm that
              You are above 12 year old.
            </Text>
            <Text style={styles.tcP}>
              This Policy is part of our Terms and Conditions and applies to the
              Personally Identifiable Information that is collected in
              connection with the download, installation and use of the
              Application and related services. By using the Application or the
              Site You signify Your assent to this Privacy Policy and consent to
              the processing of Your Personally Identifiable Information as
              described herein. If You do not agree with this Policy, or with
              our Terms of Service, do not download, install and / or use the
              Application. Your privacy is very important to us. Accordingly, we
              have developed this Policy in order for you to understand how we
              collect, use, communicate and disclose and make use of personal
              information. The following outlines our privacy policy.
            </Text>
            <Text style={styles.title}>Information Collection</Text>

            <Text style={styles.tcP}>
              "Personally Identifiable Information" means any information that
              either on its own or when combined with certain other information
              can identify an individual and for the purpose of this Privacy
              Policy usually limited to photos.
            </Text>
            <Text style={styles.tcP}>
              Friendly Chat may obtain the following types of information from
              or concerning You or Your mobile device, which may include
              personal information that can be used to identify You as specified
              below: User Provided Information: You provide certain Personally
              Identifiable Information, such as and your photo, when choosing to
              participate in the use of the Application and its services, such
              as messaging other Friendly Chat users, uploading images. These
              information may be used for the purpose of tracking spammers,
              hackers, and others who pose harm to the app/site;
            </Text>
            <Text style={styles.tcP}>
              Friendly Chat may retain date and time stamp information
              associated with successfully delivered messages.
            </Text>
            <Text style={styles.tcP}>
              The app does use third party services that may collect information
              used to identify you.
            </Text>
            <Text style={styles.title}>Cookies</Text>

            <Text style={styles.tcP}>
              Cookies are files with a small amount of data that are commonly
              used as anonymous unique identifiers. These are sent to your
              browser from the websites that you visit and are stored on your
              device's internal memory.
            </Text>
            <Text style={styles.tcP}>
              This Service does not use these cookies explicitly. However, the
              app may use third party code and libraries that use cookies to
              collect information and improve their services. You have the
              option to either accept or refuse these cookies and know when a
              cookie is being sent to your device. If you choose to refuse our
              cookies, you may not be able to use some portions of this Service.
            </Text>
            <Text style={styles.title}>Chat Privacy</Text>
            <Text style={styles.tcP}>
              At the beginning of every chat, a record is made of the fact that
              a chat occurred between you and your chat partner. This record
              includes a timestamp, details of you and your chat partner. These
              records may be used for the purpose of tracking spammers, hackers
              and others who pose harm to the app/site; and may also be used for
              law enforcement purposes.
            </Text>
            <Text style={styles.tcP}>
              Any information shared by a user to a stranger is limited to the
              user and he/she is responsible for that. Understand that strangers
              can potentially tell other people anything you tell them. So be
              careful what information you reveal to them. Do not transmit
              nudity, sexually harass anyone, publicize other peoples' private
              information, make statements that defame or libel anyone, violate
              intellectual property rights, use automated programs to start
              chats, or behave in any other inappropriate or illegal way on
              Friendly Chat. The messages (text, audio, images) are removed
              after the partner receives them or are stored if the partner is
              offline.
            </Text>
            <Text style={styles.tcP}>
              We don't validate any information like contact information,
              profile etc, so others sharing your information can't be stopped.
              It is user's responsibility.
            </Text>
            <Text style={styles.title}>Data Privacy</Text>
            <Text style={styles.tcP}>
              Before or at the time of collecting other personal information, we
              will identify the purposes for which information is being
              collected. We will collect and use personal information solely
              with the objective of fulfilling those purposes specified by us
              and for other compatible purposes, unless we obtain the consent of
              the individual concerned or as required by law. We are committed
              to conducting our business in accordance with these principles in
              order to ensure that the confidentiality of personal information
              is protected and maintained.
            </Text>
            <Text style={styles.title}>Data storage</Text>
            <Text style={styles.tcP}>
              Friendly Chat only stores the photo that you send to your partner
              for development purpose and It is not shared with any third-party.
            </Text>
            <Text style={styles.title}>Data Security</Text>
            <Text style={styles.tcP}>
              Before or at the time of collecting other personal information, we
              will identify the purposes for which information is being
              collected. We will collect and use personal information solely
              with the objective of fulfilling those purposes specified by us
              and for other compatible purposes, unless we obtain the consent of
              the individual concerned or as required by law. We are committed
              to conducting our business in accordance with these principles in
              order to ensure that the confidentiality of personal information
              is protected and maintained.
            </Text>
            <Text style={styles.tcP}>
              We value your trust in providing us your Personal Information,
              thus we are striving to use commercially acceptable means of
              protecting it. But remember that no method of transmission over
              the internet, or method of electronic storage is 100% secure and
              reliable, and I cannot guarantee its absolute security.
            </Text>
            <Text style={styles.title}>Links to Other Sites</Text>
            <Text style={styles.tcP}>
              This Service may contain links to other sites. If you click on a
              third-party link, you will be directed to that site. Note that
              these external sites are not operated by me. Therefore, I strongly
              advise you to review the Privacy Policy of these websites. I have
              no control over and assume no responsibility for the content,
              privacy policies, or practices of any third-party sites or
              services.
            </Text>
            <Text style={styles.title}>Data Access and Removal</Text>
            <Text style={styles.tcP}>
              You can always control what information you choose to share with
              us on the Friendly Chat. To do so, you can change your settings on
              your mobile device. Alternatively, you can remove the Service from
              your mobile device entirely.
            </Text>
            <Text style={styles.tcP}>
              You can remove your data and account anytime you want on your
              mobile device. If you ask us to delete your account, we will use
              commercially reasonable efforts to remove your data from our
              servers.
            </Text>
            <Text style={styles.tcP}>
              Any personally identifiable information that (i) you share in text
              messages, photos or otherwise in or through the Friendly Chat
              application with other users, bulletin board or chat room on our
              website or elsewhere, can be viewed and used by others, including
              to send you unsolicited messages or to commit identity theft.
              Friendly Chat is not responsible for any use or misuse of your
              information that might result from your disclosure of information.
            </Text>
            <Text style={styles.title}>Children’s Privacy</Text>
            <Text style={styles.tcP}>
              These Services do not address anyone under the age of 12. We do
              not knowingly collect personally identifiable information from
              children under12. In the case we discover that a child under 12
              has provided us with personal information, we immediately delete
              this from our servers. If you are a parent or guardian and you are
              aware that your child has provided us with personal information,
              please contact us so that we will be able to do necessary actions.
            </Text>
            <Text style={styles.title}>General Rules</Text>
            <Text style={styles.tcP}>
              The sending of spam, inappropriate messages or any type of
              inappropriate content is not allowed. Moderators also review
              submitted reports and profile images. Users under 12 are banned
              when detected. The sale of sexual content or any type of sexual
              service is also not allowed.
            </Text>
            <Text style={styles.title}>Date</Text>
            <Text style={styles.tcP}>
              This privacy policy was posted in October 2021.
            </Text>
          </ScrollView>

          <View>
            <TouchableOpacity onPress={this.onBack} style={styles.button}>
              <Text style={styles.buttonLabel}>Back</Text>
            </TouchableOpacity>
          </View>
        </View>
      </BackgroundSection>
    );
  }
  onBack = () => {
    this.props.navigation.goBack();
  };
}

const styles = {
  container: {
    marginTop: 20,
    marginLeft: 10,
    marginRight: 10,
    flex: 1,
  },
  title: {
    fontSize: 22,
    alignSelf: 'center',
    color: 'black',
  },
  tcP: {
    marginTop: 10,
    marginBottom: 10,
    fontSize: 12,
    color: 'black',
  },
  tcL: {
    marginLeft: 10,
    marginTop: 10,
    marginBottom: 10,
    fontSize: 12,
    color: 'black',
  },
  tcContainer: {
    flexGrow: 1,
    flexShrink: 0,
    flexBasis: 'auto',
    flex: 1,
  },

  button: {
    backgroundColor: '#136AC7',
    borderRadius: 5,
    padding: 10,
    marginVertical: 20,
  },

  buttonDisabled: {
    backgroundColor: '#999',
    borderRadius: 5,
    padding: 10,
  },

  buttonLabel: {
    fontSize: 14,
    color: '#FFF',
    alignSelf: 'center',
  },
};

export default TermsAndConditions;
