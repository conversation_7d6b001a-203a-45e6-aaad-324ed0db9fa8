import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import { StyleSheet, View, StatusBar } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import React from 'react';
import { useRecoilValue } from 'recoil';
import Room from './Room';
import AboutUsPage from './AboutUsPage';
import { RoomBadgeState } from '../store/tooltip/atoms';
import WebViewPage from './WebViewPage';
import { addGamesTab } from '../Constants/Exp';

const Tab = createMaterialTopTabNavigator();

export default function Home({ route }) {
  const roomBadge = useRecoilValue(RoomBadgeState);
  const tabBarBadge = () =>
    roomBadge ? <View style={styles.circle} /> : <></>;
  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <StatusBar barStyle="dark-content" backgroundColor="#FFFFFF" />
      <Tab.Navigator
        screenOptions={{
          swipeEnabled: false,
          lazy: true,
          tabBarStyle: styles.tabBar,
        }}>
        <Tab.Screen
          name="Chat Room"
          component={Room}
          initialParams={{ img: route.params?.img }}
          options={{
            tabBarBadge,
            tabBarBadgeStyle: styles.badge,
          }}
        />
        <Tab.Screen name="games" component={WebViewPage} />
        {/* <Tab.Screen name="quiz" component={WebViewPage} /> */}
        <Tab.Screen
          name="About Us"
          component={AboutUsPage}
          initialParams={{ img: route.params?.img }}
          options={{
            tabBarBadge,
            tabBarBadgeStyle: styles.badge,
          }}
        />
      </Tab.Navigator>
    </SafeAreaView>
  );
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  tabBar: {
    elevation: 0, // Remove shadow on Android
    shadowOpacity: 0, // Remove shadow on iOS
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  badge: {
    minWidth: 14,
    minHeight: 14,
    maxWidth: 14,
    maxHeight: 14,
    borderRadius: 7,
  },
  circle: {
    height: 10,
    width: 10,
    borderRadius: 50,
    backgroundColor: '#FDCC02',
  },
});
