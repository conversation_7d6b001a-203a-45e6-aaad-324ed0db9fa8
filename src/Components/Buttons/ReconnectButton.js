import React from 'react';
import {StyleSheet, TouchableOpacity, Text, Platform} from 'react-native';
import {useRecoilValue} from 'recoil';
import Colors from '../../Constants/Colors';
import SocketService from '../../services/socketIoService';
import {sessionState} from '../../store/session/atoms';
import {showInterstitial} from '../ads/Interstitials';
import analytics from '@react-native-firebase/analytics';
import remoteConfig from '@react-native-firebase/remote-config';
import {iapService} from '../../services/iapService';

export default function ReconnectButton() {
  const session = useRecoilValue(sessionState);
  const reconnect = () => {
    const AdInterval = remoteConfig().getValue('AdInterval').asNumber();
    analytics().logEvent('next_user');
    const partnersLenght = session.partners.length;

    // First check if user has ad-free status
    iapService
      .isAdFree()
      .then(isAdFree => {
        if (isAdFree) {
          // User has paid, skip ad
          console.log('User has ad-free status, skipping ad');
          SocketService.emit('reconnect');
        } else if (
          partnersLenght !== 0 &&
          Math.ceil(partnersLenght / 2) % AdInterval === 0
        ) {
          SocketService.emit('end');
          showInterstitial();
        } else {
          SocketService.emit('reconnect');
        }
      })
      .catch(error => {
        // On error, default to normal behavior
        console.error('Error checking ad-free status:', error);
        if (
          partnersLenght !== 0 &&
          Math.ceil(partnersLenght / 2) % AdInterval === 0
        ) {
          SocketService.emit('end');
          showInterstitial();
        } else {
          SocketService.emit('reconnect');
        }
      });
  };
  return (
    <TouchableOpacity style={styles.btn} onPress={reconnect}>
      <Text style={styles.txt}>Find new friend</Text>
    </TouchableOpacity>
  );
}
const styles = StyleSheet.create({
  btn: {
    backgroundColor: Colors.actionBtnColor,
    alignSelf: 'center',
    height: 40,
    justifyContent: 'center',
    paddingHorizontal: 10,
    marginVertical: 10,
    borderRadius: 12,
  },
  txt: {
    color: 'white',
    textAlignVertical: 'center',
    fontFamily:
      Platform.OS === 'android' ? 'PoppinsRegular-B2Bw' : 'Poppins-Regular',
  },
});
