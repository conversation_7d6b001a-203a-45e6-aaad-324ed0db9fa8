/**
 * Sample React Native App
 * https://github.com/facebook/react-native
 *
 * @format
 * @flow strict-local
 */

import React, {PureComponent} from 'react';
import {
  FlatList,
  PermissionsAndroid,
  SafeAreaView,
  TouchableOpacity,
  View,
  Image,
  Dimensions,
  Platform,
  StyleSheet,
  BackHandler,
  Text,
} from 'react-native';
import {CameraRoll} from '@react-native-camera-roll/camera-roll';
import RNFS from 'react-native-fs';
import {CommonActions, DarkTheme} from '@react-navigation/native';
import LeftArrow from '../../assets/images/leftArrow.svg';
import ActionInputMenu from '../Messages/Menu';
import ImageResizer from '@bam.tech/react-native-image-resizer';

const svgColor = DarkTheme.colors.background;
export default class Gallary extends PureComponent {
  constructor(props) {
    super(props);

    this.state = {
      Gallery: [],
      Album: [],
      img: '',
      groupname: '',
      grp: 'All',
      lastCursor: '0',
    };
  }
  backHandler;
  async componentDidMount() {
    const getCheckPermissionPromise = () => {
      if (Platform.Version >= 33) {
        return PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES);
      } else {
        return PermissionsAndroid.check(PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE);
      }
    };
    this.backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      this.onBackPress,
    );
    const getRequestPermissionPromise = () => {
      if (Platform.Version >= 33) {
        return PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES).then((status) => status === PermissionsAndroid.RESULTS.GRANTED);
      } else {
        return PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE).then((status) => status === PermissionsAndroid.RESULTS.GRANTED);
      }
    };
  
    if (Platform.OS === 'android') {
      const result = await getCheckPermissionPromise();
      if (!result) {
        // Alert.alert('Access to pictures was denied.');
        if(!await getRequestPermissionPromise()) return;
      }
    }
    let album = CameraRoll.getAlbums({
      assetType: 'Photos',
    });
    album.then(values => {
      let arr = [['All', this.getAction('All')]];
      values.sort((a, b) =>
        a.count < b.count ? 1 : b.count < a.count ? -1 : 0,
      );
      for (let i = 0; i < values.length; i++) {
        arr.push([values[i].title, this.getAction(values[i].title)]);
      }
      this.setState({Album: arr}, () => {
        this.handleLoadMore();
      });
      // }
    });
  }
  getAction = value => {
    return () => {
      this.setState(
        {
          groupname: value === 'All' ? '' : value,
          Gallery: [],
          grp: value,
          lastCursor: '0',
        },
        () => {
          this.handleLoadMore();
        },
      );
    };
  };
  componentWillUnmount() {
    this.backHandler.remove();
  }
  onBackPress = () => {
    this.props.navigation.dispatch(CommonActions.goBack());
    return true;
  };
  handleLoadMore = () => {
    let photos = CameraRoll.getPhotos({
      first: 50,
      assetType: 'Photos',
      after: this.state.lastCursor,
      groupName: this.state.groupname,
    });
    photos.then(values => {
      if (this.state.lastCursor !== values.page_info.end_cursor) {
        this.setState({
          Gallery: [...this.state.Gallery, ...values.edges],
          lastCursor: values.page_info.end_cursor,
        });
      }
    });
  };
  menuAnchor = () => <Text style={styles.folder}>{this.state.grp}</Text>;
  render() {
    return (
      <SafeAreaView>
        <View style={styles.header}>
          <TouchableOpacity
            onPress={() => {
              this.props.navigation.goBack();
            }}
            style={styles.backArrow}>
            <LeftArrow fill={svgColor} width={20} height={20} />
          </TouchableOpacity>
          <ActionInputMenu
            menuAnchor={this.menuAnchor}
            menuItems={this.state.Album}
          />
          <View />
        </View>
        <FlatList
          showsVerticalScrollIndicator={false}
          data={this.state.Gallery}
          extraData={this.state}
          onEndReachedThreshold={1}
          onEndReached={() => this.handleLoadMore()}
          renderItem={this.renderImage}
          numColumns={3}
          keyExtractor={(item, index) => String(index)}
        />
      </SafeAreaView>
    );
  }
  renderImage = ({item, index}) => {
    const onImageSelect = () => {
      ImageResizer.createResizedImage(
        item.node.image.uri,
        1920,
        1080,
        'JPEG',
        80,
        0,
        RNFS.DocumentDirectoryPath,
        false,
        {onlyScaleDown: true},
      )
        .then(async response => {
          this.props.navigation.navigate('MediaPage', {
            path: response.uri,
          });
        })
        .catch(err => {
          console.log(err);
        });
    };

    return (
      <TouchableOpacity style={styles.imgContainer} onPress={onImageSelect}>
        <Image
          resizeMode={'cover'}
          key={index}
          style={styles.img}
          source={{uri: item.node.image.uri}}
        />
      </TouchableOpacity>
    );
  };
}

const styles = StyleSheet.create({
  img: {
    alignSelf: 'center',
    width: Dimensions.get('screen').width * 0.33,
    height: Dimensions.get('screen').width * 0.33,
  },
  imgContainer: {margin: 1, backgroundColor: 'white'},
  dropdownContainer: {
    width: Dimensions.get('screen').width - 50,
    alignItems: 'center',
    color: 'black',
  },
  backArrow: {
    borderRadius: 5,
    paddingHorizontal: 5,
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'center',
    height: '100%',
    flexDirection: 'row',
  },
  header: {
    height: 55,
    marginBottom: 10,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: Dimensions.get('screen').width,
  },
  folder: {
    fontSize: 20,
    color: DarkTheme.colors.background,
    textDecorationLine: 'underline',
  },
});
