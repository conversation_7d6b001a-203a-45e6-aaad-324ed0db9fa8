import React, {useEffect} from 'react';
import {BackHandler, Dimensions} from 'react-native';
import EmojiSelector, {Categories} from '@manu_omg/react-native-emoji-selector';

const Height = Dimensions.get('window').height;
const EmojiSection = props => {
  useEffect(() => {
    const backAction = () => {
      props.onShowEmoji(false);
      return true;
    };
    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction,
    );

    return () => backHandler.remove();
  }, [props]);

  return (
    <EmojiSelector
      style={{height: Height * 0.3}}
      onEmojiSelected={props.insertEmoji}
      showSectionTitles={false}
      showSearchBar={false}
      columns={8}
      category={Categories.emotion}
    />
  );
};

export default EmojiSection;
