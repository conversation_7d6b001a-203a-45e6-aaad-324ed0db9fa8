import {useCallback, useEffect, useState} from 'react';
import {AppState} from 'react-native';

/**
 * Hook to determine if the app is in foreground
 * This is important for camera functionality as we need to
 * release camera resources when app goes to background
 */
export function useIsForeground() {
  const [isForeground, setIsForeground] = useState(true);

  const onAppStateChanged = useCallback(
    nextAppState => {
      setIsForeground(nextAppState === 'active');
    },
    [],
  );

  useEffect(() => {
    const subscription = AppState.addEventListener('change', onAppStateChanged);
    return () => subscription.remove();
  }, [onAppStateChanged]);

  return isForeground;
}
