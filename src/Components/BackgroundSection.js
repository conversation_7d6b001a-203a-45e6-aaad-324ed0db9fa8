import React from 'react';
import {
  StyleSheet,
  ImageBackground,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
const BackgroundSection = props => {
  return (
    <ImageBackground
      source={require('../assets/images/background.png')}
      imageStyle={styles.imageStyle}
      style={styles.container}>
      <SafeAreaView style={styles.container}>
        {props.children}
      </SafeAreaView>
    </ImageBackground >
  );
};
const styles = StyleSheet.create({
  container: { flex: 1, paddingBottom: 5 },
  imageStyle: { opacity: 0.5 },
});

export default BackgroundSection;
