import remoteConfig from '@react-native-firebase/remote-config';
import {AdEventType, InterstitialAd} from 'react-native-google-mobile-ads';
import SocketService from '../../services/socketIoService';
import {iapService} from '../../services/iapService';

let retryAttempt = 0;
let showAd = false;
let interstitial;
export function initializeInterstitialAds(adUnitId) {
  if (interstitial) {
    interstitial.removeAllListeners();
  }
  interstitial = InterstitialAd.createForAdRequest(
    adUnitId ?? remoteConfig().getValue('AdInterstitial').asString(),
  );
  try {
    interstitial.addAdEventListener(AdEventType.LOADED, () => {
      console.log('interstitial ad loaded');
      showAd = true;
      // Reset retry attempt
      retryAttempt = 0;
    });
    interstitial.addAdEventListener(AdEventType.ERROR, error => {
      try {
        // Interstitial ad failed to load
        // We recommend retrying with exponentially higher delays up to a maximum delay (in this case 64 seconds)
        console.log(error);
        retryAttempt += 1;
        if (retryAttempt > 5) {
          console.log(
            'Failed to load interstitial ',
            adUnitId ? 'using fall back ad unit id' : 'main ad unit id',
          );
          return initializeInterstitialAds(
            'ca-app-pub-2044352253676532/7879628744', //fall back ad unit id
          );
        }
        var retryDelay = Math.pow(2, Math.min(6, retryAttempt));

        console.log(
          'Interstitial ad failed to load - retrying in ' + retryDelay + 's',
        );

        setTimeout(function () {
          loadInterstitial();
        }, retryDelay * 1000);
      } catch (err) {
        console.log(err);
      }
    });
    interstitial.addAdEventListener(AdEventType.CLOSED, () => {
      try {
        rewardUser();
        initializeInterstitialAds();
      } catch (error) {
        console.log(error);
      }
    });

    // Load the first interstitial
    loadInterstitial();
  } catch (e) {
    console.log(e);
  }
}

function loadInterstitial() {
  interstitial.load();
}

export function showInterstitial() {
  try {
    console.log('****show ads ', showAd);
    // First check if user has ad-free status
    iapService
      .isAdFree()
      .then(isAdFree => {
        if (isAdFree) {
          // User has paid, skip ad and just reward
          console.log('User has ad-free status, skipping ad');
          rewardUser();
        } else if (showAd) {
          // Show ad only if user hasn't paid and ad is loaded
          interstitial.show();
          showAd = false;
        } else {
          rewardUser();
        }
      })
      .catch(error => {
        console.log('Error checking ad-free status:', error);
        // On error, default to rewarding user
        rewardUser();
      });
  } catch (e) {
    console.log(e);
    // On error, default to rewarding user
    rewardUser();
  }
}
const rewardUser = () => {
  SocketService.emit('reconnect');
};
