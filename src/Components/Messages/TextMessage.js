import Clipboard from '@react-native-clipboard/clipboard';
import React, {useState, useRef} from 'react';
import {
  StyleSheet,
  Text,
  ToastAndroid,
  Platform,
  Alert,
  Linking,
} from 'react-native';
import Hyperlink from 'react-native-hyperlink';
import {
  Menu,
  MenuOption,
  MenuOptions,
  MenuTrigger,
} from 'react-native-popup-menu';
import {TouchableOpacity} from 'react-native-gesture-handler';

import Colors from '../../Constants/Colors';
import {useSetRecoilState} from 'recoil';
import {deleteMessageSelector} from '../../store/session/selectors';
export default function TextMessage(props) {
  const setDeleteId = useSetRecoilState(deleteMessageSelector);
  const [visible, setVisible] = useState(false);
  const menuRef = useRef(null);

  const hideMenu = () => setVisible(false);

  const showMenu = () => {
    if (menuRef.current) {
      menuRef.current.open();
    }
    setVisible(true);
  };

  let copyToClipboard = () => {
    Clipboard.setString(props.children);
    notifyMessage('Text Copied!');
    hideMenu();
  };
  let deleteMessage = () => {
    notifyMessage('MessageReported');
    hideMenu();

    setDeleteId(props.id);
  };

  function notifyMessage(msg) {
    if (Platform.OS === 'android') {
      ToastAndroid.show(msg, ToastAndroid.SHORT);
    } else {
      Alert.alert(msg);
    }
  }
  return (
    <Menu ref={menuRef}>
      <MenuTrigger>
        <TouchableOpacity
          onPress={showMenu}
          activeOpacity={0.7}
          delayPressIn={0}>
          <Hyperlink
            onPress={url => Linking.openURL(url)}
            linkStyle={styles.link}>
            <Text style={props.style}>{props.children}</Text>
          </Hyperlink>
        </TouchableOpacity>
      </MenuTrigger>
      <MenuOptions optionsContainerStyle={styles.menuContainer}>
        <MenuOption
          style={[
            styles.menuOption,
            !props.isServerMessage ? styles.lastMenuOption : {},
          ]}
          onSelect={copyToClipboard}>
          <Text style={styles.menuText}>Copy</Text>
        </MenuOption>
        {props.isServerMessage && (
          <MenuOption
            style={[styles.menuOption, styles.lastMenuOption]}
            onSelect={deleteMessage}>
            <Text style={styles.menuText}>Report</Text>
          </MenuOption>
        )}
      </MenuOptions>
    </Menu>
  );
}
const styles = StyleSheet.create({
  font: {
    color: '#333',
    fontSize: 16,
    fontFamily:
      Platform.OS === 'ios' ? 'Poppins-SemiBold' : 'PoppinsSemiBold-B2Bw',
  },
  link: {
    color: Colors.actionBtnColor,
    textDecorationLine: 'underline',
    fontWeight: '500',
  },
  menuContainer: {
    backgroundColor: '#fff',
    borderRadius: 16,
    paddingVertical: 6,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 6},
    shadowOpacity: 0.15,
    shadowRadius: 24,
    elevation: 8,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
  },
  menuOption: {
    paddingVertical: 14,
    paddingHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    backgroundColor: '#fff',
    margin: 2,
  },
  lastMenuOption: {
    borderBottomWidth: 0,
  },
  menuText: {
    fontSize: 16,
    color: Colors.iconColor,
    fontFamily:
      Platform.OS === 'ios' ? 'Poppins-Regular' : 'PoppinsRegular-B2Bw',
  },
  hidden: {
    height: 0,
    width: 0,
    opacity: 0,
  },
});
