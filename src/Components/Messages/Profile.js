import React, {useRef} from 'react';
import {StyleSheet, Platform, TouchableOpacity, Text} from 'react-native';
import {
  Menu,
  MenuOptions,
  MenuOption,
  MenuTrigger,
} from 'react-native-popup-menu';
import SocketService from '../../services/socketIoService';
import Colors from '../../Constants/Colors';
import Avatar from './Avatar';

const Avataar = props => {
  const menuRef = useRef(null);

  return (
    <Menu ref={menuRef}>
      <MenuTrigger>
        <TouchableOpacity
          onPress={() => menuRef.current?.open()}
          activeOpacity={0.7}>
          <Avatar avatar={props.avatar} gender={props.gender} />
        </TouchableOpacity>
      </MenuTrigger>
      <MenuOptions optionsContainerStyle={styles.container}>
        <MenuOption
          style={styles.menuOption}
          onSelect={onTriggerAction('block')}>
          <Text style={styles.menuText}>Block</Text>
        </MenuOption>
        <MenuOption
          style={[styles.menuOption, styles.lastMenuOption]}
          onSelect={onTriggerAction('report')}>
          <Text style={styles.menuText}>Block and Report</Text>
        </MenuOption>
      </MenuOptions>
    </Menu>
  );

  function onTriggerAction(type) {
    return () => {
      SocketService.emit(type, {id: props.id});
    };
  }
};
const styles = StyleSheet.create({
  menuOption: {
    backgroundColor: '#ffffff',
    paddingVertical: 14,
    paddingHorizontal: 18,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    margin: 2,
  },
  lastMenuOption: {
    borderBottomWidth: 0,
  },
  menuText: {
    fontSize: 16,
    color: Colors.iconColor,
    fontFamily:
      Platform.OS === 'ios' ? 'Poppins-Regular' : 'PoppinsRegular-B2Bw',
  },
  container: {
    marginTop: 50,
    backgroundColor: '#ffffff',
    width: 220,
    borderRadius: 16,
    paddingVertical: 6,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 6,
    },
    shadowOpacity: 0.15,
    shadowRadius: 24,
    elevation: 8,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
  },
});

export default Avataar;
