import React, {useState} from 'react';
import {
  ImageBackground,
  StyleSheet,
  View,
  Modal,
  Text,
  Platform,
  PermissionsAndroid,
  ToastAndroid,
  Alert,
  Pressable,
} from 'react-native';
import ImageViewer from 'react-native-image-zoom-viewer';
import {ProgressBar} from 'react-native-paper';
import VisibilityBox from '../../assets/images/visibilityBox.svg';
import VisibilityBoxOff from '../../assets/images/visibilityBoxOff.svg';
import Colors from '../../Constants/Colors';
import RNFS from 'react-native-fs';
export default function ImageMessage(props) {
  let [blur, setBlur] = useState(props.showButton);
  let [loaded, setLoaded] = useState(false);
  let [modalVisible, setModalVisible] = useState(false);

  function getProgress() {
    let progress = props.image.progress;
    if (progress !== undefined && progress !== 100) {
      return (
        <View style={styles.progressDiv}>
          <ProgressBar
            progress={progress / 100}
            color="#3399FF"
            visible={progress !== undefined && progress !== 100}
            style={[styles.progressBar, {width: props.style.width / 2}]}
          />
        </View>
      );
    }
  }
  function getShowHideBtn() {
    return (
      props.showButton &&
      loaded && (
        <View style={styles.btnDiv}>
          <Pressable onPress={() => setBlur(!blur)}>
            {blur ? (
              <VisibilityBox fill={Colors.gray} width={44} height={27} />
            ) : (
              <VisibilityBoxOff fill={Colors.gray} width={44} height={27} />
            )}
          </Pressable>
        </View>
      )
    );
  }
  function getLoading() {
    if (!loaded) {
      if (!props.image.uri.startsWith('file')) {
        return <Text style={styles.fontFamily}>Downloading Image...</Text>;
      }
    }
  }
  async function _saveImage(uri) {
    if (Platform.OS === 'android') {
      const result = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
        {
          title: 'Permission Explanation',
          message: 'Friendly chat would like to access your pictures.',
        },
      );
      if (result !== 'granted') {
        // Alert.alert('Access to pictures was denied.');
        return;
      }
    }
    RNFS.downloadFile({
      fromUrl: uri,

      toFile: `${
        RNFS.DownloadDirectoryPath
      }/Friendly-Chat${new Date().getTime()}.JPEG`,
    })
      .promise.then(function (result) {
        if (Platform.OS === 'android') {
          ToastAndroid.showWithGravity(
            'Image saved!',
            ToastAndroid.SHORT,
            ToastAndroid.CENTER,
          );
        } else {
          Alert.alert('Image saved!');
        }
      })
      .catch(function (error) {
        if (Platform.OS === 'android') {
          ToastAndroid.showWithGravity(
            'An error occured!',
            ToastAndroid.SHORT,
            ToastAndroid.CENTER,
          );
        } else {
          Alert.alert('An error occured!');
        }

        console.log(error);
      });
  }
  return (
    <>
      <Pressable disabled={blur} onPress={() => setModalVisible(true)}>
        <ImageBackground
          source={{uri: props.image.uri}}
          imageStyle={styles.imageBorder}
          style={[styles.img, props.style]}
          blurRadius={blur ? 30 : 0}
          onLoad={() => setLoaded(true)}>
          {getShowHideBtn()}
          {getProgress()}
        </ImageBackground>
      </Pressable>
      {getLoading()}
      {blur || (
        <Modal
          visible={modalVisible}
          transparent={true}
          onRequestClose={() => setModalVisible(false)}>
          <ImageViewer
            imageUrls={[{url: props.image.uri}]}
            renderIndicator={() => {}}
            saveToLocalByLongPress={true}
            onSave={uri => _saveImage(uri)}
          />
        </Modal>
      )}
    </>
  );
}
const styles = StyleSheet.create({
  img: {
    resizeMode: 'center',
    justifyContent: 'center',
  },
  imageBorder: {borderRadius: 8},
  btn: {
    backgroundColor: '#FFFFFF',
    opacity: 0.44,
    alignItems: 'center',
    borderRadius: 5,
  },
  btnDiv: {alignItems: 'center'},
  progressDiv: {
    alignSelf: 'center',
  },
  progress: {
    fontSize: 10,
    color: Colors.gray,
  },
  fontFamily: {
    fontFamily:
      Platform.OS === 'ios' ? 'Poppins-Regular' : 'PoppinsRegular-B2Bw',
  },
  progressBar: {
    height: 5,

    backgroundColor: 'red',
  },
});
