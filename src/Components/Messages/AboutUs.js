import React from 'react';
import {AirbnbRating} from 'react-native-ratings';
import MessageTextConst from '../../Constants/MessageTextConst';
import TextMessage from './TextMessage';
import {rateApp} from '../InputSection/RateApp';
import {Platform, StyleSheet} from 'react-native';

export default function AboutUs(props) {
  const finishRating = rate => {
    if (rate === 5) {
      rateApp();
    }
  };
  return (
    <>
      <TextMessage style={styles.rateUsTxt}>
        {MessageTextConst.aboutUs}
      </TextMessage>
      <AirbnbRating
        count={5}
        reviews={['Terrible ', 'Bad ', 'Disappointing ', 'OK ', 'Good ']}
        defaultRating={4}
        onFinishRating={finishRating}
      />
      <TextMessage style={styles.rateUsTxt}>
        {MessageTextConst.helpUs}
      </TextMessage>
    </>
  );
}
const styles = StyleSheet.create({
  rateUsTxt: {
    color: 'black',
    fontSize: 16,
    fontFamily: Platform.OS === 'ios' ? 'Poppins-Regular' : 'PoppinsBold-GdJA',
  },
});
