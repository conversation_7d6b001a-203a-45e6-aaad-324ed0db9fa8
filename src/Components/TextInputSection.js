import React, { useState, useRef, useCallback } from 'react';
import { View, StyleSheet, Animated, Platform } from 'react-native';
import UserInput from './UserInput';
import EmojiSection from './InputSection/EmojiSection';
import { useEffect } from 'react';
import SocketService from '../services/socketIoService';
import { IS_LITE_VERSION } from '../config/buildConfig';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';

const TextInputSection = props => {
  const [text, setText] = useState('');
  const [showEmoji, setShowEmoji] = useState(false);
  const cursorPosition = useRef(0);
  const emojiAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (text !== '') {
      SocketService.typing(true);
    } else {
      SocketService.typing(false);
    }
  }, [text]);

  useEffect(() => {
    Animated.spring(emojiAnim, {
      toValue: showEmoji ? 1 : 0,
      useNativeDriver: true,
      friction: 8,
    }).start();
  }, [showEmoji, emojiAnim]);

  const submitEditing = useCallback(() => {
    props.onSubmit(text);
    setText('');
  }, [props, text]);

  const insertEmoji = useCallback(emoji => {
    const index = cursorPosition.current;
    setText(text.slice(0, index) + emoji + text.slice(index));
  }, [text]);

  return (
    <KeyboardAvoidingView
      behavior={"padding"}
      keyboardVerticalOffset={50}
      style={styles.container}
    >
      <View style={styles.container}>
        <UserInput
          placeholder="Send a message..."
          returnKeyType={'send'}
          onChangeText={setText}
          onSubmit={submitEditing}
          onGaralryPick={props.onAddingPhoto}
          onCameraOpen={props.onCameraShot}
          menuItems={props.menuItems}
          setShowEmoji={setShowEmoji}
          showEmoji={showEmoji}
          setCursorPosition={p => {
            cursorPosition.current = p;
          }}
          value={text}
        />
        {!IS_LITE_VERSION && showEmoji && (
          <EmojiSection insertEmoji={insertEmoji} onShowEmoji={setShowEmoji} />
        )}
      </View></KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.05)',
  },
  emojiContainer: {
    position: 'absolute',
    bottom: Platform.OS === 'ios' ? 80 : 60,
    left: 0,
    right: 0,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.05)',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 5,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
  },
});

export default TextInputSection;
