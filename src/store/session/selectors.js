import {selector, selectorFamily} from 'recoil';
import {sessionState} from './atoms';

export const messageSelector = selector({
  key: 'messageSelector',
  get: ({get}) => get(sessionState).messages,

  set: ({get, set}, messages) =>
    set(sessionState, {...get(sessionState), messages}),
});
export const partnerSelector = selectorFamily({
  key: 'partnerSelector',
  get:
    id =>
    ({get}) =>
      get(sessionState).partners[id],

  set:
    () =>
    ({get, set}, partner) =>
      set(sessionState, () => {
        const session = get(sessionState);
        const partners = [...session.partners, partner];
        return {...session, partners};
      }),
});
export const latestPartnerSelector = selector({
  key: 'latestPartnerSelector',
  get: ({get}) => {
    const partners = get(sessionState).partners;
    return partners[partners.length - 1];
  },
});
export const deleteMessageSelector = selector({
  key: 'deleteMessageSelector',
  get: () => '',

  set: ({get, set}, id) =>
    set(sessionState, () => {
      const session = get(sessionState);
      const messages = session.messages.filter(item => item.id !== id);
      return {...session, messages};
    }),
});
