<?xml version="1.0" encoding="utf-8"?>
<svg fill='none' width="21.258" height="21.257" viewBox="0 0 21.258 21.257" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>.a{fill:#2F4D7F;}</style>
    <filter id="b" x="0" y="0" width="76" height="76" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="8" result="c"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="c"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g transform="matrix(0.606545, 0, 0, 0.610867, -11.887133, -33.412777)" style="">
    <g transform="translate(1 5)">
      <rect class="a" width="34" height="34" rx="17" transform="translate(19 50)" style="opacity: 0.2;"/>
      <g class="d" transform="matrix(1, 0, 0, 1, -2, 32)" style="filter: url(#b);">
        <rect class="b" width="28" height="28" rx="14" transform="translate(24 21)" style="fill: rgb(255, 255, 255);"/>
      </g>
    </g>
  </g>
  <path class="a" d="M 15.735 9.911 L 11.229 9.911 L 11.229 5.405 C 11.229 4.83 10.606 4.471 10.108 4.758 C 9.877 4.892 9.735 5.138 9.735 5.405 L 9.735 9.911 L 5.229 9.911 C 4.654 9.911 4.294 10.534 4.582 11.032 C 4.715 11.263 4.962 11.405 5.229 11.405 L 9.735 11.405 L 9.735 15.911 C 9.735 16.486 10.357 16.846 10.855 16.558 C 11.086 16.425 11.229 16.178 11.229 15.911 L 11.229 11.405 L 15.735 11.405 C 16.31 11.405 16.669 10.783 16.382 10.285 C 16.248 10.053 16.002 9.911 15.735 9.911 Z" />
</svg>