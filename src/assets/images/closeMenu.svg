<?xml version="1.0" encoding="utf-8"?>
<svg fill='none' width="21.258" height="21.257" viewBox="0 0 21.258 21.257" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>.a{fill:#2F4D7F;}</style>
    <filter id="b" x="0" y="0" width="76" height="76" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="8" result="c"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="c"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g transform="matrix(0.604776, 0.001086, -0.001087, 0.604819, -11.684778, -32.992752)" style="">
    <g transform="translate(1 5)">
      <rect class="a" width="34" height="34" rx="17" transform="translate(19 50)" style="opacity: 0.2;"/>
      <g class="d" transform="matrix(1, 0, 0, 1, -2, 32)" style="filter: url(#b);">
        <rect class="b" width="28" height="28" rx="14" transform="translate(24 21)" style="fill: rgb(255, 255, 255);"/>
      </g>
    </g>
  </g>
  <path class="a" d="M 15.823 9.823 L 11.317 9.823 L 11.317 5.317 C 11.317 4.742 10.695 4.382 10.197 4.67 C 9.965 4.803 9.823 5.05 9.823 5.317 L 9.823 9.823 L 5.317 9.823 C 4.742 9.823 4.383 10.445 4.67 10.943 C 4.804 11.174 5.05 11.317 5.317 11.317 L 9.823 11.317 L 9.823 15.823 C 9.823 16.398 10.446 16.757 10.944 16.47 C 11.175 16.336 11.317 16.09 11.317 15.823 L 11.317 11.317 L 15.823 11.317 C 16.398 11.317 16.758 10.694 16.47 10.196 C 16.337 9.965 16.09 9.823 15.823 9.823 Z" style="" transform="matrix(0.707107, 0.707107, -0.707107, 0.707107, 10.569914, -4.378446)"/>
</svg>