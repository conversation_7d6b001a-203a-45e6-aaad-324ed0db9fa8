const { getDefaultConfig, mergeConfig } = require("@react-native/metro-config");

const {
    wrapWithReanimatedMetroConfig,
  } = require('react-native-reanimated/metro-config');
  const defaultConfig = getDefaultConfig(__dirname);
  const { assetExts, sourceExts } = defaultConfig.resolver;
  
  const config = {
    transformer: {
      babelTransformerPath: require.resolve(
        "react-native-svg-transformer/react-native"
      )
    },
    resolver: {
      assetExts: assetExts.filter((ext) => ext !== "svg"),
      sourceExts: [...sourceExts, "svg"]
    }
  };
  
  module.exports =wrapWithReanimatedMetroConfig( mergeConfig(defaultConfig, config));
  